import{d as e,Z as a,r as s,w as t,o,g as r,q as l,v as m,c as u,U as d,T as i,e as c,l as n}from"./index-PITnHt5Y.js";/* empty css              *//* empty css            */import{E as p,a as h}from"./select-D0Ysbh2X.js";/* empty css                  *//* empty css               */import{o as f}from"./order-BLlr5hsb.js";import{g as v}from"./index-C45PtPps.js";import{b as I}from"./enums-BQfqlMjU.js";const j=e({__name:"CustomSelectBusiness",setup(e){const j=f(),{ListParams:g,BasicData:S}=a(j),b=s([]),w=()=>{S.value.platformListWidthShop=[...I,{name:"其他",id:99}].filter((e=>(e.nick=e.name,e.children=b.value.find((e=>e.customerId===g.value.shopId)).shopStoreList.filter((a=>a.platform===e.id)),e.children.length>0)))};return t((()=>g.value.shopId),(e=>{(b.value.find((a=>a.customerId===e))||{}).customerName&&n.set("factoryShopId",e)}),{immediate:!0}),o((()=>{(async()=>{const e=await v({permission:"TRADE"}).catch((()=>({})));if(b.value=e.data,!b.value[0])return;const a=n.get("factoryShopId");a&&b.value.find((e=>e.customerId===a))?g.value.shopId=a:e.data.length&&!g.value.shopId&&(g.value.shopId=e.data[0].customerId),w(),j.GetListAndStatus()})()})),(e,a)=>{const s=p,t=h;return r(),l(t,{placeholder:"选择客户",class:"w-full",filterable:"",modelValue:c(g).shopId,"onUpdate:modelValue":a[0]||(a[0]=e=>c(g).shopId=e),onChange:w},{default:m((()=>[(r(!0),u(i,null,d(b.value,(e=>(r(),l(s,{key:e.customerId,value:e.customerId,label:`${e.customerNick||e.customerName}${e.contactPhone?"/"+e.contactPhone:""}${e.customerCode?"/"+e.customerCode:""}`},null,8,["value","label"])))),128))])),_:1},8,["modelValue"])}}});export{j as _};
