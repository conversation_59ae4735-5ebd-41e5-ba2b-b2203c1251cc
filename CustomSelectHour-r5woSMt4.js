import{d as e,Z as o,r as s,w as a,g as r,c as t,y as l,v as u,T as n,U as i,e as m,x as p,n as d,_ as c}from"./index-PITnHt5Y.js";/* empty css                     *//* empty css              *//* empty css            */import{E as j,a as h}from"./select-D0Ysbh2X.js";/* empty css                  *//* empty css               */import{o as x}from"./order-BLlr5hsb.js";import{E as v}from"./index-DL4e0n8Q.js";import"./index-ClQNu5pa.js";import"./index-BKVJYL4F.js";import"./util-CyoWC6l2.js";import"./index-BgeYWFc3.js";import"./strings-OM41d7cj.js";import"./debounce-BlDc_s1D.js";import"./_baseFindIndex-BTqrMG7V.js";import"./_baseIteratee-CPEO3oEA.js";import"./index-zBmKjUyn.js";import"./order-1J6GWiyf.js";import"./enums-BQfqlMjU.js";import"./index-C45PtPps.js";import"./storePick-B3x7-wa7.js";import"./index-DRH_4TEs.js";const b={key:0,class:"w-[80px] leading-[14px] text-danger ml-[8px]"},g=c(e({__name:"CustomSelectHour",setup(e){const c=x(),{ListParams:g}=o(c),V=[{id:0,name:"付款后"},{id:1,name:"发货后"}],S=s(""),_=s(!1),E=s(!1),F=(e="")=>{"hourStartFocus"===e&&(_.value=!1),"hourEndFocus"===e&&(_.value=!1),S.value=(()=>{const{hourStart:e,hourEnd:o}=g.value;return e&&o?e&&o&&e>=o?"error":"active":""})()};return a((()=>g.value),(()=>F()),{immediate:!0}),(e,o)=>{const s=j,a=h,c=v;return r(),t("div",{class:d(["custom-select-hour",S.value])},[l(a,{class:"num-select select-bg","popper-class":"num-select",modelValue:m(g).hourType,"onUpdate:modelValue":o[0]||(o[0]=e=>m(g).hourType=e),placeholder:"请选择方式"},{default:u((()=>[(r(),t(n,null,i(V,(e=>l(s,{key:e.id,label:e.name,value:e.id},null,8,["label","value"]))),64))])),_:1},8,["modelValue"]),l(c,{controls:!0,"controls-position":"right",class:"num-input",modelValue:m(g).hourStart,"onUpdate:modelValue":o[1]||(o[1]=e=>m(g).hourStart=e),min:1,max:999,placeholder:"开始时间",onFocus:o[2]||(o[2]=e=>_.value=!0),onBlur:o[3]||(o[3]=e=>F("hourStartFocus"))},null,8,["modelValue"]),l(c,{controls:!0,"controls-position":"right",class:"num-input",modelValue:m(g).hourEnd,"onUpdate:modelValue":o[4]||(o[4]=e=>m(g).hourEnd=e),min:m(g).hourStart+1,max:1e3,onFocus:o[5]||(o[5]=e=>E.value=!0),placeholder:"结束时间",onBlur:o[6]||(o[6]=e=>F("hourEndFocus"))},null,8,["modelValue","min"]),"error"===S.value?(r(),t("div",b,"结束时间不能小于开始时间")):p("",!0)],2)}}}),[["__scopeId","data-v-0d99bec2"]]);export{g as default};
