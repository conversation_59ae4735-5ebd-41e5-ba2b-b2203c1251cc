import{_ as e}from"./Cascader.vue_vue_type_style_index_0_lang-D5cw9zSR.js";import{o as s}from"./order-BLlr5hsb.js";import{d as t,Z as l,r as a,w as o,m as r,g as i,q as u,n}from"./index-PITnHt5Y.js";const c=t({__name:"CustomSelectStore",setup(t){const c=s(),{ListParams:d,BasicData:m}=l(c),p={multiple:!0,value:"id",label:"nick",emitPath:!1},v=a([]),_=e=>{d.value.storeIdList=e.some((e=>"ALL"===e))?[]:e};o((()=>d.value),((e,s)=>{JSON.stringify(e.storeIdList)!==JSON.stringify(s.storeIdList)&&(v.value=e.storeIdList)}));const f=r((()=>m.value.platformListWidthShop.filter((e=>125!==e.id))));return(s,t)=>{const l=e;return i(),u(l,{class:n(["custom-select-order-store !w-full",{active:!!v.value.length}]),modelValue:v.value,"onUpdate:modelValue":t[0]||(t[0]=e=>v.value=e),options:f.value,props:p,selectAll:"",filterSelectAll:"","collapse-tags":"","collapse-tags-tooltip":"",clearable:"",placeholder:"请选择店铺",onChange:_},null,8,["class","modelValue","options"])}}});export{c as _};
