{"filterOptions": [{"id": 0, "name": "选择客户", "isLong": true, "show": true, "modelValue1": "", "modelValue2": "", "componentsName": "CustomSelectBusiness", "multiple": false, "clear": false, "options": []}, {"id": 1, "name": "订单号", "isLong": true, "show": true, "modelValue1": "tidStrs", "modelValue2": "", "componentsName": "CustomInput", "multiple": true, "clear": true}, {"id": 2, "name": "订单类型", "isLong": false, "show": true, "modelValue1": "orderType", "modelValue2": "", "componentsName": "CustomSelect", "multiple": false, "clear": true, "options": [{"id": 0, "name": "正常订单"}, {"id": 1, "name": "合并订单"}, {"id": 2, "name": "手工订单"}, {"id": 3, "name": "复制订单"}, {"id": 4, "name": "来图订单"}, {"id": 5, "name": "导入订单"}, {"id": 6, "name": "拆单订单"}, {"id": 8, "name": "反审拆单"}]}, {"id": 3, "name": "是否有留言备注", "isLong": false, "show": true, "modelValue1": "remark<PERSON><PERSON>y", "modelValue2": "", "componentsName": "CustomSelect", "multiple": false, "clear": true, "options": [{"id": 6, "name": "无留言且无备注"}, {"id": 5, "name": "有留言或有备注"}, {"id": 4, "name": "无留言"}, {"id": 3, "name": "有留言"}, {"id": 2, "name": "无备注"}, {"id": 1, "name": "有备注"}]}, {"id": 4, "name": "付款/承诺/推送时间", "isLong": true, "show": true, "modelValue1": "timeBegin", "modelValue2": "timeEnd", "componentsName": "CustomDatePicker", "multiple": false, "clear": false}, {"id": 5, "name": "店铺", "isLong": true, "show": true, "modelValue1": "platformQueries", "modelValue2": "", "componentsName": "CustomSelectStore", "multiple": true, "clear": true, "options": [{"id": 0, "name": "淘宝"}, {"id": 7, "name": "拼多多"}, {"id": 9, "name": "抖音"}, {"id": 13, "name": "淘特"}, {"id": 5, "name": "阿里"}, {"id": 11, "name": "快手"}, {"id": 8, "name": "京东"}, {"id": 15, "name": "小红书"}, {"id": 16, "name": "TikTok"}, {"id": 17, "name": "<PERSON>ee"}, {"id": 18, "name": "<PERSON><PERSON><PERSON>"}, {"id": 19, "name": "速卖通"}, {"id": 20, "name": "淘工厂"}, {"id": 21, "name": "微信小店"}, {"id": 25, "name": "<PERSON><PERSON>(全托管)"}, {"id": 125, "name": "<PERSON><PERSON>(半托管)"}, {"id": 225, "name": "<PERSON><PERSON>(半托管-美区)"}, {"id": 325, "name": "<PERSON><PERSON>(半托管-欧区)"}, {"id": 425, "name": "<PERSON><PERSON>(美国-本土)"}, {"id": 525, "name": "<PERSON><PERSON>(半托管-全球)"}, {"id": 26, "name": "Shein"}, {"id": 126, "name": "<PERSON><PERSON>(半托管)"}, {"id": 27, "name": "VipShop"}, {"id": 28, "name": "Etsy"}, {"id": 30, "name": "<PERSON><PERSON>"}, {"id": 31, "name": "得物"}, {"id": 119, "name": "速卖通(全托管)"}, {"id": 219, "name": "速卖通(半托管)"}, {"id": 116, "name": "TikTok(全托管)"}, {"id": 4, "name": "京喜"}, {"id": 35, "name": "阿里国际站"}, {"id": 1, "name": "禾量"}, {"id": 2, "name": "美客多"}, {"id": 6, "name": "奇门"}]}, {"id": 6, "name": "选择快递", "isLong": false, "show": true, "modelValue1": "cpCode", "modelValue2": "", "componentsName": "CustomSelect", "multiple": false, "clear": true, "options": []}, {"id": 7, "name": "快递面单", "isLong": false, "show": true, "modelValue1": "waybillType", "modelValue2": "", "componentsName": "CustomSelect", "multiple": false, "clear": true, "options": [{"id": 1, "name": "超级面单"}, {"id": 0, "name": "工厂面单"}]}, {"id": 8, "name": "平台订单状态", "isLong": false, "show": true, "modelValue1": "outerOrderStatusList", "modelValue2": "", "componentsName": "CustomSelect", "multiple": true, "clear": true, "options": [{"id": 0, "name": "待发货"}, {"id": 1, "name": "已发货"}, {"id": 2, "name": "部分发货"}, {"id": 3, "name": "交易完成"}, {"id": 4, "name": "交易关闭"}]}, {"id": 9, "name": "生产状态", "isLong": false, "show": true, "modelValue1": "shipStatusOrder", "modelValue2": "", "componentsName": "CustomSelect", "multiple": false, "clear": true, "options": [{"id": 0, "name": "未打包"}, {"id": 1, "name": "已打包"}, {"id": 2, "name": "未推单"}, {"id": 3, "name": "已推单"}]}, {"id": 10, "name": "快递单号", "isLong": true, "show": true, "modelValue1": "cpNumStrs", "modelValue2": "", "componentsName": "CustomInput", "multiple": true, "clear": true}, {"id": 11, "name": "是否有快递单号", "isLong": false, "show": true, "modelValue1": "existCpNum", "modelValue2": "", "componentsName": "CustomSelect", "multiple": false, "clear": true, "options": [{"id": true, "name": "有快递单号"}, {"id": false, "name": "无快递单号"}]}, {"id": 12, "name": "商品标题", "isLong": true, "show": true, "modelValue1": "inquiryModeByTitle", "modelValue2": "title", "componentsName": "CustomMateInput", "multiple": false, "clear": false}, {"id": 13, "name": "商家编码", "isLong": true, "show": true, "modelValue1": "inquiryModeBySkuCode", "modelValue2": "shopMappingSku", "modelValue3": "shopMappingSkuRange", "componentsName": "CustomMateInput", "multiple": false, "clear": false}, {"id": 14, "name": "原商家编码(SKU货号/商品SKU)", "isLong": true, "show": true, "modelValue1": "inquiryModeByOuterIid", "modelValue2": "outerIid", "componentsName": "CustomMateInput", "multiple": false, "clear": false}, {"id": 15, "name": "面单打印状态", "isLong": false, "show": true, "modelValue1": "waybillPrintStatus", "modelValue2": "", "componentsName": "CustomSelect", "multiple": false, "clear": true, "options": [{"id": true, "name": "已打印"}, {"id": false, "name": "未打印"}]}, {"id": 16, "name": "拿货条码", "isLong": true, "show": true, "modelValue1": "barcode", "modelValue2": "", "componentsName": "CustomInput", "multiple": true, "clear": true}, {"id": 17, "name": "收货姓名", "isLong": false, "show": true, "modelValue1": "<PERSON><PERSON><PERSON>", "modelValue2": "", "componentsName": "CustomInput", "multiple": false, "clear": true}, {"id": 18, "name": "收货手机号", "isLong": false, "show": true, "modelValue1": "receiverMobileStrs", "modelValue2": "", "componentsName": "CustomInput", "multiple": false, "clear": true}, {"id": 19, "name": "买家昵称", "isLong": false, "show": true, "modelValue1": "buyerNickStrs", "modelValue2": "", "componentsName": "CustomInput", "multiple": false, "clear": true}, {"id": 20, "name": "卖家备注", "isLong": false, "show": true, "modelValue1": "shopRemark", "modelValue2": "", "componentsName": "CustomInput", "multiple": false, "clear": true}, {"id": 21, "name": "买家留言", "isLong": false, "show": true, "modelValue1": "buyerRemark", "modelValue2": "", "componentsName": "CustomInput", "multiple": false, "clear": true}, {"id": 22, "name": "厂家备注", "isLong": false, "show": true, "modelValue1": "factoryRemark", "modelValue2": "", "componentsName": "CustomInput", "multiple": false, "clear": true}, {"id": 23, "name": "商品数量", "isLong": true, "show": true, "modelValue1": "numEqualType", "modelValue2": "num", "componentsName": "CustomSelectNumber", "multiple": false, "clear": true}, {"id": 24, "name": "锁单状态", "isLong": false, "show": true, "modelValue1": "lockStatusOrder", "modelValue2": "", "componentsName": "CustomSelect", "multiple": false, "clear": true, "options": [{"id": true, "name": "已锁单"}, {"id": false, "name": "未锁单"}]}, {"id": 25, "name": "作废状态", "isLong": false, "show": true, "modelValue1": "discardStatusOrder", "modelValue2": "", "componentsName": "CustomSelect", "multiple": false, "clear": true, "options": [{"id": 0, "name": "未作废"}, {"id": 1, "name": "已作废"}, {"id": 2, "name": "生产中已退款"}, {"id": 3, "name": "已打包已退款"}]}, {"id": 26, "name": "组包状态", "isLong": false, "show": false, "modelValue1": "milePackageStatus", "modelValue2": "", "componentsName": "CustomSelect", "multiple": false, "clear": true, "options": [{"id": -1, "name": "未组包"}, {"id": 0, "name": "未上传组包"}, {"id": 1, "name": "已上传组包"}, {"id": 3, "name": "已揽收组包"}]}, {"id": 27, "name": "大包批次号", "isLong": false, "show": false, "modelValue1": "bigPackageLPNumberStrs", "modelValue2": "", "componentsName": "CustomInput", "multiple": true, "clear": true}, {"id": 28, "name": "插旗备注", "isLong": true, "show": true, "modelValue1": "flag<PERSON>ist<PERSON><PERSON><PERSON>", "modelValue2": "", "componentsName": "CustomFlag", "multiple": false, "clear": false}, {"id": 29, "name": "省份、城市", "isLong": true, "show": true, "modelValue1": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "modelValue2": "receiverCity", "componentsName": "CustomArea", "multiple": false, "clear": false}, {"id": 30, "name": "图号是否正确", "isLong": false, "show": true, "modelValue1": "isPicNum", "modelValue2": "", "componentsName": "CustomSelect", "multiple": false, "clear": true, "options": [{"id": 1, "name": "图号正确"}, {"id": 0, "name": "图号错误"}]}, {"id": 31, "name": "商品规格", "isLong": true, "show": true, "modelValue1": "inquiryModeByProperties", "modelValue2": "skuProperties", "modelValue3": "skuPropertiesRange", "componentsName": "CustomMateInput", "multiple": false, "clear": false}, {"id": 32, "name": "Temu紧急状态", "isLong": false, "show": true, "modelValue1": "urgencyType", "modelValue2": "", "componentsName": "CustomSelect", "multiple": false, "clear": true, "options": [{"id": 1, "name": "紧急备货单"}, {"id": 0, "name": "普通备货单"}]}, {"id": 33, "name": "选择工厂", "isLong": false, "show": true, "modelValue1": "factoryId", "modelValue2": "", "componentsName": "CustomSelect", "multiple": false, "clear": true, "options": []}, {"id": 34, "name": "托管状态", "isLong": false, "show": false, "modelValue1": "custodyType", "modelValue2": "", "componentsName": "CustomSelect", "multiple": false, "clear": true, "options": [{"id": 2, "name": "全托管"}, {"id": 1, "name": "半托管"}]}, {"id": 35, "name": "时间倒计时", "isLong": true, "show": true, "modelValue1": "hourType", "modelValue2": "", "componentsName": "CustomSelectHour", "multiple": false, "clear": true}, {"id": 36, "name": "物流状态", "isLong": false, "show": true, "modelValue1": "logisticsStatus", "modelValue2": "", "componentsName": "CustomSelect", "multiple": false, "clear": true, "options": [{"id": 0, "name": "未揽收"}, {"id": 1, "name": "已揽收"}, {"id": 2, "name": "运输中"}, {"id": 3, "name": "派送中"}, {"id": 4, "name": "已签收"}]}, {"id": 37, "name": "是否定制单", "isLong": false, "show": true, "modelValue1": "isCustomGoods", "modelValue2": "", "componentsName": "CustomSelect", "multiple": false, "clear": true, "options": [{"id": 1, "name": "是"}, {"id": 0, "name": "否"}]}, {"id": 38, "name": "解码状态", "isLong": false, "show": true, "modelValue1": "decodeStatus", "modelValue2": "", "componentsName": "CustomSelect", "multiple": false, "clear": true, "options": [{"id": 1, "name": "解码成功"}, {"id": 2, "name": "解码失败"}]}, {"id": 39, "name": "订单标签", "isLong": false, "show": false, "modelValue1": "tagList<PERSON><PERSON><PERSON>", "modelValue2": "", "componentsName": "CustomSelect", "multiple": true, "clear": true, "options": [{"id": 1, "name": "今日可发货"}, {"id": 2, "name": "精选联盟"}, {"id": 4, "name": "小店自卖"}, {"id": 16, "name": "全托管仓发订单"}, {"id": 32, "name": "首单"}, {"id": 64, "name": "平台审核中"}, {"id": 256, "name": "半托管仓发订单"}, {"id": 512, "name": "抽检不合格"}, {"id": 1024, "name": "因抽检不合格创建"}, {"id": 2048, "name": "淘宝分销-明文订单"}, {"id": 4096, "name": "淘宝分销-密文订单"}, {"id": 8192, "name": "抖音分销-明文订单"}, {"id": 16384, "name": "抖音分销-密文订单"}, {"id": 32768, "name": "京东分销-明文订单"}, {"id": 65536, "name": "京东分销-密文订单"}, {"id": 131072, "name": "拼多多分销-明文订单"}, {"id": 262144, "name": "拼多多分销-密文订单"}, {"id": 524288, "name": "快手分销-明文订单"}, {"id": 1048576, "name": "快手分销-密文订单"}, {"id": 2097152, "name": "小红书分销-明文订单"}, {"id": 4194304, "name": "小红书分销-密文订单"}, {"id": 8388608, "name": "shopee履约"}, {"id": 16777216, "name": "Y2订单"}, {"id": 33554432, "name": "卖家履约订单"}, {"id": 67108864, "name": "合作对接仓履约订单"}, {"id": 134217728, "name": "奇门-聚水潭OMS"}]}, {"id": 41, "name": "是否JIT", "isLong": false, "show": false, "modelValue1": "jitFlag", "modelValue2": "", "componentsName": "CustomSelect", "multiple": false, "clear": true, "options": [{"id": 1, "name": "是"}, {"id": 0, "name": "否"}]}, {"id": 42, "name": "是否VMI", "isLong": false, "show": false, "modelValue1": "vmiFlag", "modelValue2": "", "componentsName": "CustomSelect", "multiple": false, "clear": true, "options": [{"id": 1, "name": "是"}, {"id": 0, "name": "否"}]}, {"id": 43, "name": "<PERSON><PERSON>紧急状态", "isLong": false, "show": false, "modelValue1": "sheinSettlementType", "modelValue2": "", "componentsName": "CustomSelect", "multiple": false, "clear": true, "options": [{"id": 1, "name": "备货"}, {"id": 0, "name": "急采"}]}, {"id": 44, "name": "商品id", "isLong": true, "show": true, "modelValue1": "numIids", "modelValue2": "", "componentsName": "CustomInput", "multiple": true, "clear": true}, {"id": 45, "name": "实付金额", "isLong": true, "show": true, "modelValue1": "paymentEqualType", "modelValue2": "payment", "componentsName": "CustomSelectNumber", "multiple": false, "clear": true}, {"id": 46, "name": "是否标记工厂缺图", "isLong": false, "show": true, "modelValue1": "factoryLackPicStatus", "modelValue2": "", "componentsName": "CustomSelect", "multiple": false, "clear": true, "options": [{"id": true, "name": "是"}, {"id": false, "name": "否"}]}, {"id": 47, "name": "解码失败原因", "isLong": true, "show": true, "modelValue1": "decodeErrorTypeList", "modelValue2": "", "componentsName": "CustomSelect", "multiple": true, "clear": true, "options": [{"id": 1, "name": "未填写四要素的失败原因", "children": [{"id": 1, "name": "未填写【材质】编码"}, {"id": 2, "name": "未填写【颜色】编码"}, {"id": 4, "name": "未填写【型号】编码"}, {"id": 8, "name": "未填写【图号】编码"}]}, {"id": 2, "name": "四要素在商家编码库中不存在的失败原因", "children": [{"id": 16, "name": "【材质】商家编码库中不存在"}, {"id": 32, "name": "【颜色】商家编码库中不存在"}, {"id": 64, "name": "【型号】商家编码库中不存在"}, {"id": 128, "name": "【图号】商家编码库中不存在"}]}, {"id": 3, "name": "四要素组成的SKU在工厂端未上架", "children": [{"id": 256, "name": "工厂未上架该商品"}, {"id": 512, "name": "工厂已下架该商品"}]}, {"id": 4, "name": "图号编码有问题的失败原因", "children": [{"id": 1024, "name": "图号必须传线上图推送"}, {"id": 2048, "name": "图号未含有姓氏信息"}]}, {"id": 5, "name": "赠品编码有问题的失败原因", "children": [{"id": 4096, "name": "【赠品】商家编码库中不存在"}, {"id": 8192, "name": "【赠品】在工厂编码映射失败"}]}, {"id": 6, "name": "赠品打图有问题的失败原因", "children": [{"id": 16384, "name": "【赠品】没有选择赠品图片"}, {"id": 32768, "name": "【赠品】图片在图库中不存在"}, {"id": 65536, "name": "【赠品】图片必须传线上图推送"}]}, {"id": 7, "name": "四要素在商家编码中存在，但和工厂映射失败", "children": [{"id": 131072, "name": "【材质】编码在工厂映射失败"}, {"id": 262144, "name": "【颜色】编码在工厂映射失败"}, {"id": 524288, "name": "【型号】编码在工厂映射失败"}]}]}, {"id": 48, "name": "推送类型", "isLong": false, "show": true, "modelValue1": "autoPush", "modelValue2": "", "componentsName": "CustomSelect", "multiple": false, "clear": true, "options": [{"id": 0, "name": "手动推送"}, {"id": 1, "name": "自动推送"}]}, {"id": 49, "name": "缺货状态", "isLong": false, "show": true, "modelValue1": "inventoryStatus", "modelValue2": "", "componentsName": "CustomSelect", "multiple": false, "clear": true, "options": [{"id": 1, "name": "缺货"}, {"id": 2, "name": "有货"}]}, {"id": 50, "name": "temu发货单状态", "isLong": false, "show": true, "modelValue1": "jitSendStatus", "modelValue2": "", "componentsName": "CustomSelect", "multiple": false, "clear": true, "options": [{"id": 15, "name": "待接单"}, {"id": -1, "name": "待加入发货台"}, {"id": 0, "name": "待创建"}, {"id": 14, "name": "创建中"}, {"id": 1, "name": "待发货"}, {"id": 2, "name": "待收货"}]}, {"id": 51, "name": "货号/供方货号", "isLong": true, "show": true, "modelValue1": "inquiryModeByProductSn", "modelValue2": "productSn", "componentsName": "CustomMateInput", "multiple": true, "clear": false}, {"id": 52, "name": "类目", "isLong": true, "show": true, "modelValue1": "categoryIdList", "modelValue2": "", "componentsName": "CustomCategoryV2", "multiple": true, "clear": true}, {"id": 53, "name": "定制ID", "isLong": true, "show": true, "modelValue1": "customIdStr", "modelValue2": "", "componentsName": "CustomInput", "multiple": true, "clear": true}, {"id": 54, "name": "有无赠品", "isLong": false, "show": true, "modelValue1": "hasGift", "modelValue2": "", "componentsName": "CustomSelect", "multiple": false, "clear": true, "options": [{"id": true, "name": "有赠品"}, {"id": false, "name": "无赠品"}]}, {"id": 55, "name": "组合商品", "isLong": false, "show": true, "modelValue1": "combinationGoodsType", "modelValue2": "", "componentsName": "CustomSelect", "multiple": false, "clear": true, "options": [{"id": 0, "name": "含有组合商品"}, {"id": 1, "name": "没有组合商品"}]}, {"id": 56, "name": "赠品编码", "isLong": true, "show": true, "modelValue1": "inquiryModeByGiftCode", "modelValue2": "giftCode", "componentsName": "CustomMateInput", "multiple": false, "clear": true}, {"id": 57, "name": "半托库存扣减仓库", "isLong": false, "show": true, "modelValue1": "deductWarehouseName", "modelValue2": "", "componentsName": "CustomSelect", "multiple": false, "clear": true, "filter": true, "options": []}, {"id": 58, "name": "自定义订单备注", "isLong": true, "show": true, "modelValue1": "customOrderRemark", "modelValue2": "", "componentsName": "CustomSelect", "multiple": false, "clear": true, "filter": true, "options": []}]}