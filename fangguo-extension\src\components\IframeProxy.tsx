'use client';

import { useEffect, useState, useRef } from 'react';
import { useSearchParams } from 'next/navigation';

const IframeProxy: React.FC = () => {
  const searchParams = useSearchParams();
  const iframeRef = useRef<HTMLIFrameElement>(null);
  const [targetUrl, setTargetUrl] = useState<string>('');
  const [inputUrl, setInputUrl] = useState<string>('');
  const [status, setStatus] = useState<string>('等待输入URL...');
  const [isInjected, setIsInjected] = useState<boolean>(false);

  // 默认目标URL
  const DEFAULT_URL = 'https://fangguo.com/business/order';

  useEffect(() => {
    // 从URL参数获取目标URL
    const urlParam = searchParams.get('url');
    if (urlParam) {
      setTargetUrl(urlParam);
      setInputUrl(urlParam);
      setStatus('正在加载页面...');
    } else {
      setInputUrl(DEFAULT_URL);
    }
  }, [searchParams]);

  // 注入脚本到iframe
  const injectScript = () => {
    try {
      const iframe = iframeRef.current;
      if (!iframe || !iframe.contentWindow) {
        setStatus('无法访问iframe内容');
        return;
      }

      const iframeDoc = iframe.contentWindow.document;
      
      // 检查是否已经注入过
      if (iframeDoc.querySelector('.fangguo-custom-input')) {
        setStatus('输入框已存在');
        setIsInjected(true);
        return;
      }

      // 查找目标元素
      const targetDiv = iframeDoc.querySelector('div[data-v-76b09359].flex-shrink-0');
      
      if (!targetDiv) {
        setStatus('未找到目标元素，可能页面还未完全加载或结构已变化');
        return;
      }

      // 创建输入框容器
      const inputContainer = iframeDoc.createElement('div');
      inputContainer.className = 'fangguo-custom-input flex items-center mr-4';
      
      // 创建输入框
      const input = iframeDoc.createElement('input');
      input.type = 'text';
      input.placeholder = '请输入搜索内容...';
      input.className = 'px-3 py-2 border border-gray-300 rounded-md text-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent';
      input.style.width = '200px';
      
      // 创建搜索按钮
      const searchBtn = iframeDoc.createElement('button');
      searchBtn.textContent = '搜索';
      searchBtn.className = 'ml-2 px-4 py-2 bg-blue-500 text-white rounded-md text-sm hover:bg-blue-600 focus:outline-none focus:ring-2 focus:ring-blue-500';
      
      // 添加搜索功能
      const handleSearch = () => {
        const value = input.value.trim();
        if (value) {
          // 在iframe中显示搜索结果
          iframe.contentWindow?.alert(`搜索内容: ${value}`);
          // 这里可以添加实际的搜索逻辑
        } else {
          iframe.contentWindow?.alert('请输入搜索内容');
        }
      };
      
      searchBtn.addEventListener('click', handleSearch);
      input.addEventListener('keypress', (e) => {
        if (e.key === 'Enter') {
          handleSearch();
        }
      });
      
      // 组装元素
      inputContainer.appendChild(input);
      inputContainer.appendChild(searchBtn);
      
      // 插入到目标位置（第一个子元素之前）
      targetDiv.insertBefore(inputContainer, targetDiv.firstChild);
      
      setStatus('输入框注入成功！');
      setIsInjected(true);
    } catch (error) {
      console.error('注入脚本时出错:', error);
      setStatus(`注入失败: ${error}`);
    }
  };

  // iframe加载完成后的处理
  const handleIframeLoad = () => {
    setStatus('页面加载完成，尝试注入输入框...');
    
    // 延迟注入，等待页面完全渲染
    setTimeout(() => {
      injectScript();
    }, 2000);

    // 定期尝试注入（防止页面动态加载）
    const interval = setInterval(() => {
      if (!isInjected) {
        injectScript();
      } else {
        clearInterval(interval);
      }
    }, 3000);

    // 10秒后停止尝试
    setTimeout(() => {
      clearInterval(interval);
    }, 10000);
  };

  // 处理URL输入
  const handleUrlSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    if (inputUrl.trim()) {
      const newUrl = `${window.location.origin}?url=${encodeURIComponent(inputUrl.trim())}`;
      window.location.href = newUrl;
    }
  };

  // 如果没有URL参数，显示输入界面
  if (!targetUrl) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-gray-50">
        <div className="max-w-md w-full mx-4">
          <div className="bg-white rounded-lg shadow-lg p-8">
            <h1 className="text-2xl font-bold mb-6 text-center text-gray-800">
              房果网页面增强工具
            </h1>
            
            <form onSubmit={handleUrlSubmit} className="space-y-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  请输入要访问的URL:
                </label>
                <input
                  type="url"
                  value={inputUrl}
                  onChange={(e) => setInputUrl(e.target.value)}
                  placeholder="https://fangguo.com/business/order"
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                  required
                />
              </div>
              
              <button
                type="submit"
                className="w-full bg-blue-500 text-white py-2 px-4 rounded-md hover:bg-blue-600 focus:outline-none focus:ring-2 focus:ring-blue-500"
              >
                打开页面
              </button>
            </form>
            
            <div className="mt-6 p-4 bg-blue-50 rounded-lg">
              <h3 className="font-medium text-blue-800 mb-2">功能说明:</h3>
              <ul className="text-sm text-blue-700 space-y-1">
                <li>• 在iframe中加载目标网站</li>
                <li>• 自动在指定位置注入搜索输入框</li>
                <li>• 支持房果网业务订单页面增强</li>
              </ul>
            </div>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="h-screen flex flex-col">
      {/* 顶部状态栏 */}
      <div className="bg-white border-b border-gray-200 p-4">
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-4">
            <h1 className="text-lg font-semibold text-gray-800">房果网页面增强</h1>
            <div className="flex items-center space-x-2">
              <div className={`w-2 h-2 rounded-full ${isInjected ? 'bg-green-500' : 'bg-yellow-500'}`}></div>
              <span className="text-sm text-gray-600">{status}</span>
            </div>
          </div>
          
          <div className="flex items-center space-x-2">
            <span className="text-sm text-gray-500">目标URL:</span>
            <span className="text-sm font-mono bg-gray-100 px-2 py-1 rounded">
              {targetUrl}
            </span>
            <button
              onClick={() => window.location.href = window.location.origin}
              className="text-sm bg-gray-500 text-white px-3 py-1 rounded hover:bg-gray-600"
            >
              返回
            </button>
          </div>
        </div>
      </div>
      
      {/* iframe容器 */}
      <div className="flex-1">
        <iframe
          ref={iframeRef}
          src={targetUrl}
          className="w-full h-full border-0"
          onLoad={handleIframeLoad}
          title="Target Website"
        />
      </div>
    </div>
  );
};

export default IframeProxy;
