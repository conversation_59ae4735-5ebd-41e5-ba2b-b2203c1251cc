import{d as e,Z as l,r as a,w as o,g as t,c as s,b as d,y as u,v as m,T as i,U as r,e as n,q as p,x as c,ce as V,z as v,n as y,h5 as f,f as j,aV as b,Q as x,a6 as g,_}from"./index-PITnHt5Y.js";/* empty css              *//* empty css            */import{E as k,a as h}from"./select-D0Ysbh2X.js";/* empty css                  *//* empty css               */import{o as w}from"./order-BLlr5hsb.js";import"./index-ClQNu5pa.js";import"./index-BKVJYL4F.js";import"./util-CyoWC6l2.js";import"./index-BgeYWFc3.js";import"./strings-OM41d7cj.js";import"./debounce-BlDc_s1D.js";import"./_baseFindIndex-BTqrMG7V.js";import"./_baseIteratee-CPEO3oEA.js";import"./index-zBmKjUyn.js";import"./order-1J6GWiyf.js";import"./enums-BQfqlMjU.js";import"./index-C45PtPps.js";import"./storePick-B3x7-wa7.js";const U={class:"box-border w-full"},S={key:0},C={class:"pt-[24px] pb-[12px]"},q=_(e({__name:"CustomMateInput",props:{name:{type:String,default:"请输入"},modelValue1:{type:String,required:!0},modelValue2:{type:String,required:!0},modelValue3:{type:String,required:!1},multiple:{type:Boolean,default:!1}},setup(e){const _=e,q=w(),{ListParams:I}=l(q),M=[{id:0,name:"模糊匹配"},{id:1,name:"精准匹配"}],R=[{id:1,name:"等于"},{id:0,name:"不等于"}],z=a(_.multiple);"inquiryModeByOuterIid"===_.modelValue1&&o((()=>I.value[_.modelValue1]),(e=>{z.value=!!e}));const B=a(""),H=a(!1),K=()=>{B.value=I.value[_.modelValue2],H.value=!0},L=()=>{I.value[_.modelValue2]=B.value,H.value=!1},P=()=>q.SearchListAndCutStatus();return(l,a)=>{const o=k,_=h,w=b,q=x,Y=g;return t(),s("div",U,[d("div",{class:y(["custom-mate-input",{active:n(f)(n(I)[e.modelValue2])}])},[u(_,{class:"mate-select select-bg","popper-class":"mate-select",modelValue:n(I)[e.modelValue1],"onUpdate:modelValue":a[0]||(a[0]=l=>n(I)[e.modelValue1]=l),placeholder:"请选择方式"},{default:m((()=>[(t(),s(i,null,r(M,(e=>u(o,{key:e.id,label:e.name,value:e.id},null,8,["label","value"]))),64))])),_:1},8,["modelValue"]),["商家编码","商品规格"].includes(e.name)?(t(),p(_,{key:0,class:"mate-select select-bg small","popper-class":"mate-select",modelValue:n(I)[e.modelValue3],"onUpdate:modelValue":a[1]||(a[1]=l=>n(I)[e.modelValue3]=l),placeholder:"请选择范围"},{default:m((()=>[(t(),s(i,null,r(R,(e=>u(o,{key:e.id,label:e.name,value:e.id},null,8,["label","value"]))),64))])),_:1},8,["modelValue"])):c("",!0),z.value?(t(),p(w,{key:1,class:"mate-input",modelValue:n(I)[e.modelValue2],"onUpdate:modelValue":a[2]||(a[2]=l=>n(I)[e.modelValue2]=l),placeholder:`${e.name}'，多个值以逗号隔开或空格隔开`,clearable:"",onKeydown:v(P,["enter"])},V({_:2},[z.value?{name:"append",fn:m((()=>[d("span",{onClick:K},"•••")])),key:"0"}:void 0]),1032,["modelValue","placeholder"])):(t(),p(w,{key:2,class:"mate-input",modelValue:n(I)[e.modelValue2],"onUpdate:modelValue":a[3]||(a[3]=l=>n(I)[e.modelValue2]=l),modelModifiers:{trim:!0},placeholder:`${e.name}`,clearable:"",onKeydown:v(P,["enter"])},null,8,["modelValue","placeholder"]))],2),z.value?(t(),s("div",S,[u(Y,{class:"order-dialog",modelValue:H.value,"onUpdate:modelValue":a[6]||(a[6]=e=>H.value=e),width:"480px",title:e.name,"align-center":"",draggable:"","close-on-click-modal":!1},{footer:m((()=>[d("div",C,[u(q,{type:"info",plain:"",onClick:a[5]||(a[5]=e=>H.value=!1)},{default:m((()=>a[7]||(a[7]=[j("取消")]))),_:1}),u(q,{class:"!ml-[14px]",type:"primary",onClick:L},{default:m((()=>a[8]||(a[8]=[j("确认")]))),_:1})])])),default:m((()=>[a[9]||(a[9]=d("div",{class:"py-[12px] text-fill-900",style:{"font-family":"MicrosoftYaHei, sans-serif"}}," 多个值以逗号隔开或空格隔开 ",-1)),u(w,{modelValue:B.value,"onUpdate:modelValue":a[4]||(a[4]=e=>B.value=e),type:"textarea",autosize:{minRows:3,maxRows:5},placeholder:"多个用逗号或空格分隔"},null,8,["modelValue"])])),_:1},8,["modelValue","title"])])):c("",!0)])}}}),[["__scopeId","data-v-afd57b41"]]);export{q as default};
