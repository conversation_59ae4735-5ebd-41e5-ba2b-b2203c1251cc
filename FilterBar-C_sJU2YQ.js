import{d as e,s as a,aW as l,Z as t,r as o,w as s,l as i,C as m,o as n,$ as u,a0 as r,A as p,g as d,c,b as f,e as g,y,v,T as h,q as V,f as x,x as C,n as w,U as b,af as S,aa as j,a1 as _,t as N,a2 as k,ai as L,z as I,d3 as M,ds as T,F as O,d5 as P,gW as B,gX as D,ah as q,gY as z,a8 as U,bp as J,Q as F,ae as E,aV as Y,a6 as A,aA as H,du as R,_ as $}from"./index-PITnHt5Y.js";/* empty css                *//* empty css              */import{E as G}from"./drawer-Bi93e4-d.js";import{_ as Q}from"./DragSort-BRCPSGKL.js";/* empty css                  */import{I as K,u as W,o as X}from"./order-BLlr5hsb.js";import{_ as Z}from"./UpdateOrder.vue_vue_type_style_index_0_lang-CCDywcI3.js";import{_ as ee}from"./ExportOrder.vue_vue_type_script_setup_true_lang-C9X3JypY.js";import{u as ae}from"./enums-BQfqlMjU.js";import{_ as le}from"./CustomArea.vue_vue_type_style_index_0_lang-Bdkrwuzc.js";import te from"./CustomDatePicker-CuIGWmEm.js";import oe from"./CustomFlag-CIoieFJA.js";import se from"./CustomInput-B-6HBF3O.js";import ie from"./CustomSelect-BF9B2MjA.js";import me from"./CustomSelectNumber-CfjNcMkK.js";import{_ as ne}from"./CustomSelectStore.vue_vue_type_style_index_0_lang-DNPD2XDs.js";import ue from"./CustomMateInput-pTfe61UW.js";import re from"./CustomSelectHour-r5woSMt4.js";import{_ as pe}from"./CustomSelectBusiness.vue_vue_type_script_setup_true_lang-Bomw79XV.js";import de from"./CustomCategoryV2-Bk370ppa.js";import{I as ce}from"./index-BNdcQi1T.js";import{i as fe}from"./importTemplate-Ch1Ycp5I.js";import ge from"./ExpireTime-D-PN8TAu.js";import ye from"./RollNotice-vzcgTPVH.js";import{k as ve}from"./order-1J6GWiyf.js";import{a as he,b as Ve}from"./index-CH2Obcys.js";import{E as xe}from"./index-BKVJYL4F.js";import{v as Ce}from"./directive-CD4GlS8h.js";import"./index-C45PtPps.js";import"./storePick-B3x7-wa7.js";import"./tab-pane-C04CWHWi.js";import"./strings-OM41d7cj.js";import"./index-BnQGl6OW.js";/* empty css             */import"./Cascader.vue_vue_type_style_index_0_lang-D5cw9zSR.js";import"./cascader-B-tAq_AR.js";import"./cascader-panel-Ct3vSbjE.js";import"./arrays-C13NE5Pp.js";import"./cloneDeep-RynYmZg7.js";import"./_baseClone-HNt-rG8w.js";import"./_initCloneObject-BLxXFCHG.js";import"./index-ClQNu5pa.js";import"./index-BgeYWFc3.js";import"./index-zBmKjUyn.js";import"./debounce-BlDc_s1D.js";/* empty css               *//* empty css            *//* empty css              */import"./form-item-DTfWLJ4l.js";import"./castArray-DrghVnAp.js";import"./date-picker-9YDhSY9i.js";import"./panel-time-pick-BlfY8P5x.js";import"./index-DRH_4TEs.js";import"./select-D0Ysbh2X.js";import"./_baseFindIndex-BTqrMG7V.js";import"./_baseIteratee-CPEO3oEA.js";import"./alert-BqysZy6I.js";import"./task-5CKDHvei.js";import"./MyPager-DHpHNhyl.js";import"./pagination-DMwDBD7O.js";/* empty css              *//* empty css                *//* empty css             *//* empty css                     */import"./task-B0gMQzGK.js";import"./index-DKsu-bzA.js";import"./isArrayLikeObject-DvkEM-YU.js";import"./raf-dik5npiF.js";import"./SelectCity-BDUhDP7u.js";/* empty css                       *//* empty css                     */import"./index-DL4e0n8Q.js";import"./HSelectAll.vue_vue_type_script_setup_true_lang-BdcuERHd.js";import"./HSelect-DZyzFyGN.js";/* empty css                */import"./LazyPopover.vue_vue_type_script_setup_true_lang-CrnxwbmD.js";import"./index-ATwNd7CN.js";import"./LoadingBtn-Cc_EI-PW.js";import"./upload-D2r4LsqI.js";import"./progress-qgHb1Al-.js";import"./link-DK7ZR3cF.js";import"./uploadOss-DJQZ1FBx.js";import"./mountDynamicComponent-5mGASWnd.js";import"./index-CFZu4pPT.js";import"./carousel-item-CSOelnLM.js";import"./throttle-C-o6lUwH.js";import"./notice-BAoTFwve.js";import"./util-CyoWC6l2.js";const we=[{id:0,name:"选择客户",isLong:!0,show:K(),modelValue1:"",modelValue2:"",componentsName:"CustomSelectBusiness",multiple:!1,clear:!1},{id:1,name:"订单号",isLong:!0,show:!0,modelValue1:"tidStrs",modelValue2:"",componentsName:"CustomInput",multiple:!0,clear:!0},{id:2,name:"订单类型",isLong:!1,show:!0,modelValue1:"orderType",modelValue2:"",componentsName:"CustomSelect",multiple:!1,clear:!0},{id:3,name:"是否有留言备注",isLong:!1,show:!0,modelValue1:"remarkQuery",modelValue2:"",componentsName:"CustomSelect",multiple:!1,clear:!0},{id:4,name:"付款/承诺/推送时间",isLong:!0,show:!0,modelValue1:"timeBegin",modelValue2:"timeEnd",componentsName:"CustomDatePicker",multiple:!1,clear:!1},{id:5,name:"店铺",isLong:!0,show:!0,modelValue1:"platformQueries",modelValue2:"",componentsName:"CustomSelectStore",multiple:!0,clear:!0},{id:6,name:"选择快递",isLong:!1,show:!0,modelValue1:"cpCode",modelValue2:"",componentsName:"CustomSelect",multiple:!1,clear:!0},{id:7,name:"快递面单",isLong:!1,show:!0,modelValue1:"waybillType",modelValue2:"",componentsName:"CustomSelect",multiple:!1,clear:!0},{id:8,name:"平台订单状态",isLong:!1,show:!0,modelValue1:"outerOrderStatusList",modelValue2:"",componentsName:"CustomSelect",multiple:!0,clear:!0},{id:9,name:"生产状态",isLong:!1,show:!0,modelValue1:"shipStatusOrder",modelValue2:"",componentsName:"CustomSelect",multiple:!1,clear:!0},{id:10,name:"快递单号",isLong:!0,show:!0,modelValue1:"cpNumStrs",modelValue2:"",componentsName:"CustomInput",multiple:!0,clear:!0},{id:11,name:"是否有快递单号",isLong:!1,show:!0,modelValue1:"existCpNum",modelValue2:"",componentsName:"CustomSelect",multiple:!1,clear:!0},{id:12,name:"商品标题",isLong:!0,show:!0,modelValue1:"inquiryModeByTitle",modelValue2:"title",componentsName:"CustomMateInput",multiple:!1,clear:!1},{id:13,name:"商家编码",isLong:!0,show:!0,modelValue1:"inquiryModeBySkuCode",modelValue2:"shopMappingSku",modelValue3:"shopMappingSkuRange",componentsName:"CustomMateInput",multiple:!1,clear:!1},{id:14,name:"原商家编码(SKU货号/商品SKU)",isLong:!0,show:!0,modelValue1:"inquiryModeByOuterIid",modelValue2:"outerIid",componentsName:"CustomMateInput",multiple:!1,clear:!1},{id:15,name:"面单打印状态",isLong:!1,show:!0,modelValue1:"waybillPrintStatus",modelValue2:"",componentsName:"CustomSelect",multiple:!1,clear:!0},{id:16,name:"拿货条码",isLong:!0,show:!0,modelValue1:"barcode",modelValue2:"",componentsName:"CustomInput",multiple:!0,clear:!0},{id:17,name:"收货姓名",isLong:!1,show:!0,modelValue1:"receiverName",modelValue2:"",componentsName:"CustomInput",multiple:!1,clear:!0},{id:18,name:"收货手机号",isLong:!1,show:!0,modelValue1:"receiverMobileStrs",modelValue2:"",componentsName:"CustomInput",multiple:!1,clear:!0},{id:19,name:"买家昵称",isLong:!1,show:!0,modelValue1:"buyerNickStrs",modelValue2:"",componentsName:"CustomInput",multiple:!1,clear:!0},{id:20,name:"卖家备注",isLong:!1,show:!0,modelValue1:"shopRemark",modelValue2:"",componentsName:"CustomInput",multiple:!1,clear:!0},{id:21,name:"买家留言",isLong:!1,show:!0,modelValue1:"buyerRemark",modelValue2:"",componentsName:"CustomInput",multiple:!1,clear:!0},{id:22,name:"厂家备注",isLong:!1,show:!0,modelValue1:"factoryRemark",modelValue2:"",componentsName:"CustomInput",multiple:!1,clear:!0},{id:23,name:"商品数量",isLong:!0,show:!0,modelValue1:"numEqualType",modelValue2:"num",componentsName:"CustomSelectNumber",multiple:!1,clear:!0},{id:24,name:"锁单状态",isLong:!1,show:!0,modelValue1:"lockStatusOrder",modelValue2:"",componentsName:"CustomSelect",multiple:!1,clear:!0},{id:25,name:"作废状态",isLong:!1,show:!0,modelValue1:"discardStatusOrder",modelValue2:"",componentsName:"CustomSelect",multiple:!1,clear:!0},{id:26,name:"组包状态",isLong:!1,show:!1,modelValue1:"milePackageStatus",modelValue2:"",componentsName:"CustomSelect",multiple:!1,clear:!0},{id:27,name:"大包批次号",isLong:!1,show:!1,modelValue1:"bigPackageLPNumberStrs",modelValue2:"",componentsName:"CustomInput",multiple:!0,clear:!0},{id:28,name:"插旗备注",isLong:!0,show:!0,modelValue1:"flagListQuery",modelValue2:"",componentsName:"CustomFlag",multiple:!1,clear:!1},{id:29,name:"省份、城市",isLong:!0,show:!0,modelValue1:"receiverProvince",modelValue2:"receiverCity",componentsName:"CustomArea",multiple:!1,clear:!1},{id:30,name:"图号是否正确",isLong:!1,show:!0,modelValue1:"isPicNum",modelValue2:"",componentsName:"CustomSelect",multiple:!1,clear:!0},{id:31,name:"商品规格",isLong:!0,show:!0,modelValue1:"inquiryModeByProperties",modelValue2:"skuProperties",modelValue3:"skuPropertiesRange",componentsName:"CustomMateInput",multiple:!1,clear:!1},{id:32,name:"Temu紧急状态",isLong:!1,show:!0,modelValue1:"urgencyType",modelValue2:"",componentsName:"CustomSelect",multiple:!1,clear:!0},{id:33,name:"选择工厂",isLong:!1,show:!0,modelValue1:"factoryId",modelValue2:"",componentsName:"CustomSelect",multiple:!1,clear:!0},{id:34,name:"托管状态",isLong:!1,show:!1,modelValue1:"custodyType",modelValue2:"",componentsName:"CustomSelect",multiple:!1,clear:!0},{id:35,name:"时间倒计时",isLong:!0,show:!0,modelValue1:"hourType",modelValue2:"",componentsName:"CustomSelectHour",multiple:!1,clear:!0},{id:36,name:"物流状态",isLong:!1,show:!0,modelValue1:"logisticsStatus",modelValue2:"",componentsName:"CustomSelect",multiple:!1,clear:!0},{id:37,name:"是否定制单",isLong:!1,show:!0,modelValue1:"isCustomGoods",modelValue2:"",componentsName:"CustomSelect",multiple:!1,clear:!0},{id:38,name:"解码状态",isLong:!1,show:!0,modelValue1:"decodeStatus",modelValue2:"",componentsName:"CustomSelect",multiple:!1,clear:!0},{id:39,name:"订单标签",isLong:!1,show:!1,modelValue1:"tagListQuery",modelValue2:"",componentsName:"CustomSelect",multiple:!0,clear:!0},{id:41,name:"是否JIT",isLong:!1,show:!1,modelValue1:"jitFlag",modelValue2:"",componentsName:"CustomSelect",multiple:!1,clear:!0},{id:42,name:"是否VMI",isLong:!1,show:!1,modelValue1:"vmiFlag",modelValue2:"",componentsName:"CustomSelect",multiple:!1,clear:!0},{id:43,name:"Shein紧急状态",isLong:!1,show:!1,modelValue1:"sheinSettlementType",modelValue2:"",componentsName:"CustomSelect",multiple:!1,clear:!0},{id:44,name:"商品id",isLong:!0,show:!0,modelValue1:"numIids",modelValue2:"",componentsName:"CustomInput",multiple:!0,clear:!0},{id:45,name:"实付金额",isLong:!0,show:!0,modelValue1:"paymentEqualType",modelValue2:"payment",componentsName:"CustomSelectNumber",multiple:!1,clear:!0},{id:46,name:"是否标记工厂缺图",isLong:!1,show:!0,modelValue1:"factoryLackPicStatus",modelValue2:"",componentsName:"CustomSelect",multiple:!1,clear:!0},{id:47,name:"解码失败原因",isLong:!0,show:!0,modelValue1:"decodeErrorTypeList",modelValue2:"",componentsName:"CustomSelect",multiple:!0,clear:!0},{id:48,name:"推送类型",isLong:!1,show:!0,modelValue1:"autoPush",modelValue2:"",componentsName:"CustomSelect",multiple:!1,clear:!0},{id:49,name:"缺货状态",isLong:!1,show:!0,modelValue1:"inventoryStatus",modelValue2:"",componentsName:"CustomSelect",multiple:!1,clear:!0},{id:50,name:"temu发货单状态",isLong:!1,show:!0,modelValue1:"jitSendStatus",modelValue2:"",componentsName:"CustomSelect",multiple:!1,clear:!0},{id:51,name:"货号/供方货号",isLong:!0,show:!0,modelValue1:"inquiryModeByProductSn",modelValue2:"productSn",componentsName:"CustomMateInput",multiple:!0,clear:!1},{id:52,name:"类目",isLong:!0,show:!0,modelValue1:"categoryIdList",modelValue2:"",componentsName:"CustomCategoryV2",multiple:!0,clear:!0},{id:53,name:"定制ID",isLong:!0,show:!0,modelValue1:"customIdStr",modelValue2:"",componentsName:"CustomInput",multiple:!0,clear:!0},{id:54,name:"有无赠品",isLong:!1,show:!0,modelValue1:"hasGift",modelValue2:"",componentsName:"CustomSelect",multiple:!1,clear:!0},{id:55,name:"组合商品",isLong:!1,show:!0,modelValue1:"combinationGoodsType",modelValue2:"",componentsName:"CustomSelect",multiple:!1,clear:!0},{id:56,name:"赠品编码",isLong:!0,show:!0,modelValue1:"inquiryModeByGiftCode",modelValue2:"giftCode",componentsName:"CustomMateInput",multiple:!1,clear:!0},{id:57,name:"半托库存扣减仓库",isLong:!1,show:!0,modelValue1:"deductWarehouseName",modelValue2:"",componentsName:"CustomSelect",multiple:!1,clear:!0,filter:!0},{id:58,name:"自定义订单备注",isLong:!0,show:!0,modelValue1:"customOrderRemark",modelValue2:"",componentsName:"CustomSelect",multiple:!1,clear:!0,filter:!0}],be=()=>we,Se=e=>{const a=JSON.parse(JSON.stringify(e)).filter((e=>e.show)),l=[];let t=[];return a.forEach((e=>{e.isLong?(t.length&&(l.push(t),t=[]),l.push([e])):t.length?(l.push([...t,e]),t=[]):t.push(e)})),t.length&&l.push(t),l},je={class:"order-filter-bar h-auto"},_e={class:"flex items-center justify-between px-[10px] pt-[10px]"},Ne={key:0,class:"flex flex-1 items-center"},ke={class:"notice ml-[5px] flex-1 max-w-[600px]"},Le={key:1},Ie={class:"flex-shrink-0 flex items-center justify-end"},Me={class:"search-area-wrap"},Te={class:"h-[48px] flex items-center justify-between px-[10px] bg-white"},Oe={class:"flex-shrink-0"},Pe=["onClick"],Be={class:"flex-shrink-0"},De={class:"w-full flex items-center justify-between"},qe={class:"flex justify-between items-center"},ze={class:"flex items-center"},Ue={class:"flex justify-end items-center"},Je={class:"flex items-center justify-center text-[14px] py-[24px] text-fill-900",style:{"font-family":"MicrosoftYaHei, sans-serif"}},Fe={class:"pb-[12px]"},Ee={class:"mx-[65px] mt-[8px]"},Ye={class:"leading-8"},Ae={class:"mt-[18px]"},He={class:"mt-[8px]"},Re={class:"pb-[12px]"},$e="business-order",Ge=$(e({__name:"FilterBar",setup(e){const{saveListParamsSortConfig:$}=W(),K=a(),Ge=l({CustomArea:le,CustomDatePicker:te,CustomFlag:oe,CustomInput:se,CustomSelect:ie,CustomSelectNumber:me,CustomSelectStore:ne,CustomMateInput:ue,CustomSelectHour:re,CustomSelectBusiness:pe,CustomCategoryV2:de}),Qe=X(),{ListParams:Ke,BasicData:We,ManualOrderData:Xe,Theme:Ze,BatchOrderData:ea,Config:aa}=t(Qe),la=o(!1),ta=o(!1),oa=o(!1),sa=o(!1),ia=()=>sa.value=!sa.value,ma=o(!1),na=o(be()),ua=async()=>{const{data:e}=await M($e).catch((()=>({})));if(!e)return;const a=Object.fromEntries(we.map((e=>[e.id,e]))),l=JSON.parse(e).map((e=>({...a[e.id],show:e.show}))),t=l.map((e=>e.id)),o=be().filter((e=>!t.includes(e.id)));na.value=aa.value.IsFactory?[...o,...l]:[...l,...o]},ra=o({importType:1,shopTenantId:null}),pa=async()=>{const{data:e}=await T(225);try{if(e){const a=JSON.parse(JSON.parse(e));ra.value.importType=a.importType||1}}catch(a){ra.value.importType=1}ce({title:"订单导入",uploadFn:ve,templateUrl:fe.bOrderImport,urlKey:"fileUrl",size:"large",params:ra,tip:{type:"warning",text:"1. 如果要修改订单，请确保表格中的订单号在平台存在\n2. 导入时可以只用填写【收件人地址】，平台会自动解析省市区、收货人和电话信息"}},{setup(){const e=()=>y("div",null,[y("div",{class:"flex mt-[10px] px-[5px] items-baseline"},[y("div",{class:"text-nowrap"},[x("编码适配规则：")]),y(he,{modelValue:ra.value.importType,onChange:e=>{ra.value.importType=e,(()=>{const e={importType:ra.value.importType};R({commonConfigJson:JSON.stringify(e),type:225})})()}},{default:()=>[y(Ve,{label:1},{default:()=>[y("div",{class:"flex items-center"},[y("span",{class:"mr-1"},[x("适配商家编码“材质-颜色-型号-图号”")])])]}),y(Ve,{label:2},{default:()=>[y("div",{class:"flex items-center"},[y("span",{class:"mr-1"},[x("适配商家编码“工厂ID-材质-型号-颜色-图号”")])])]}),y(Ve,{label:3},{default:()=>[y("div",{class:"flex items-center"},[y("span",{class:"mr-1"},[x("适配商家编码“材质-型号-颜色-图号”")])])]})]})])]);return()=>H(e)}})};s((()=>Ke.value.shopId),(async e=>{if(e){K.setPageLoading(!0);try{ra.value.shopTenantId=Ke.value.shopId,await Qe.GetBasicData(),Oa()}catch(a){K.setPageLoading(!1)}}}));const da=o(!1),ca=o([]),fa=o(!1),ga=o(!0),ya=()=>{ca.value=JSON.parse(JSON.stringify(na.value.filter((e=>e.id)))),da.value=!0},va=e=>ca.value=e,ha=()=>{const e=ca.value.filter((e=>e.show));fa.value=ca.value.length===e.length,ga.value=!!e.length&&e.length<ca.value.length},Va=e=>{ca.value=ca.value.map((a=>({...a,show:e}))),ga.value=!1},xa=async()=>{if(ca.value.every((e=>!e.show)))return O.warning("请选择搜索条件");ma.value=!0;const e={settingType:$e,settingValue:JSON.stringify(ca.value)},{data:a}=await P(e).catch((()=>({})));ma.value=!1,a&&(O.success("修改成功"),na.value=JSON.parse(JSON.stringify(ca.value)),await ua(),Ca())},Ca=()=>{da.value=!1,ca.value=[],fa.value=!1,ga.value=!0},wa=()=>{ca.value=be().filter((e=>e.id)),ha()},ba=o(!1),Sa=o(0),ja=()=>{Sa.value=Ke.value.sort,ba.value=!0},_a=()=>{ba.value=!1,Ke.value.sort=Sa.value,Oa(),$()},Na=o(!1),ka=o(""),La=o([]),Ia=()=>{Na.value=!1,ka.value=""},Ma=async()=>{const{data:e}=await B($e).catch((()=>({})));e&&(La.value=e.map((e=>({...e,infoData:JSON.parse(e.infoData)})))||[])},Ta=async()=>{ma.value=!0;const e={pageFlag:$e,infoName:ka.value,infoData:JSON.stringify(Ke.value)},{data:a}=await D(e).catch((()=>({})));ma.value=!1,a&&(O.success("保存成功"),Ia(),await Ma())},Oa=()=>Qe.SearchListAndCutStatus(),Pa=async()=>{const{inquiryModeByTitle:e,inquiryModeBySkuCode:a,inquiryModeByOuterIid:l,inquiryModeByProperties:t,pageSize:o}=Qe.ListParams;Qe.ListParams={...JSON.parse(JSON.stringify(Qe.ListParamsDefault)),queryStatus:Qe.Status,inquiryModeByTitle:e,inquiryModeBySkuCode:a,inquiryModeByOuterIid:l,inquiryModeByProperties:t,pageSize:o,shopId:Qe.ListParams.shopId,sort:Sa.value},await Qe.GetListAndStatus(!0)},Ba=()=>{la.value=!0},Da=()=>{oa.value=!0,la.value=!1},qa=o(!1),za=o(""),Ua=`diy.fangguo.com/${i.get(m.TENANT)}/`,Ja=o(Ua),Fa=()=>{Ja.value=Ua+za.value},Ea=()=>{Ja.value=Ua,za.value=""};return n((async()=>{ma.value=!0,await ua(),await Ma(),ma.value=!1})),(e,a)=>{const l=F,t=xe,o=E,s=Q,i=G,m=Y,n=A,M=u("WarnTriangleFilled"),T=r("auth"),P=Ce;return p((d(),c("div",je,[f("div",_e,[g(aa).IsFactory?(d(),c("div",Le)):(d(),c("div",Ne,[a[25]||(a[25]=f("div",{class:"text-[18px] text-fill-900 font-bold whitespace-nowrap"},"我的订单",-1)),f("div",ke,[y(ye,{transition:"transform 6s ease-in-out"},{prepend:v((()=>a[24]||(a[24]=[f("i",{class:"iconfont icon-guangbo text-primary ml-[8px]"},null,-1)]))),_:1})])])),f("div",Ie,["Default"===g(Ze)?(d(),c(h,{key:0},[p((d(),V(l,{class:"btn",type:"primary",onClick:a[0]||(a[0]=e=>g(Xe).show=!0)},{default:v((()=>a[26]||(a[26]=[x("手工单")]))),_:1})),[[T,"business:order:manualOrder"]]),g(Qe).Config.customization?(d(),V(l,{key:0,class:"btn",type:"primary",onClick:a[1]||(a[1]=e=>qa.value=!0)},{default:v((()=>a[27]||(a[27]=[x("定制链接")]))),_:1})):C("",!0),y(l,{class:"btn",type:"primary",onClick:a[2]||(a[2]=e=>g(ea).show=!0)},{default:v((()=>a[28]||(a[28]=[x("批量单")]))),_:1}),y(l,{class:"btn",type:"primary",onClick:a[3]||(a[3]=e=>ta.value=!0)},{default:v((()=>a[29]||(a[29]=[x("订单更新")]))),_:1}),y(l,{class:"btn",type:"warning",onClick:pa},{default:v((()=>a[30]||(a[30]=[x("导入订单")]))),_:1}),y(l,{class:"btn",type:"warning",onClick:Ba},{default:v((()=>a[31]||(a[31]=[x("导出订单")]))),_:1})],64)):"Lite"===g(Ze)?(d(),c(h,{key:1},[y(ge,{class:"mr-[24px]"}),p((d(),V(l,{class:"lite-btn no-border !bg-fill-900 !text-white hover:opacity-[0.68]",onClick:a[4]||(a[4]=e=>g(Xe).show=!0)},{default:v((()=>a[32]||(a[32]=[x("手工单")]))),_:1})),[[T,"business:order:manualOrder"]]),g(Qe).Config.customization?(d(),V(l,{key:0,class:"lite-btn",plain:"",type:"primary",onClick:a[5]||(a[5]=e=>qa.value=!0)},{default:v((()=>a[33]||(a[33]=[x("定制链接")]))),_:1})):C("",!0),y(l,{class:"lite-btn",type:"primary",plain:"",onClick:a[6]||(a[6]=e=>g(ea).show=!0)},{default:v((()=>a[34]||(a[34]=[x("批量单")]))),_:1}),y(l,{class:"lite-btn",type:"primary",plain:"",onClick:a[7]||(a[7]=e=>ta.value=!0)},{default:v((()=>a[35]||(a[35]=[x("订单更新")]))),_:1}),y(l,{class:"lite-btn !bg-page",plain:"",onClick:pa},{default:v((()=>a[36]||(a[36]=[x("导入订单")]))),_:1}),y(l,{class:"lite-btn !bg-page",plain:"",onClick:Ba},{default:v((()=>a[37]||(a[37]=[x("导出订单")]))),_:1})],64)):C("",!0)])]),f("div",Me,[f("div",{class:w(["search-area",sa.value?"h-auto":"h-[48px] overflow-hidden"])},[(d(!0),c(h,null,b(g(Se)(na.value),((e,a)=>(d(),c("div",{class:"search-col",key:a},[(d(!0),c(h,null,b(e,((a,l)=>(d(),c("div",{key:a.modelValue1+`-${l}-`+a.id,class:w(["search-item",e.length>1?"more":""])},[(d(),V(j(Ge.value[a.componentsName]),S({ref_for:!0},{...a,id:a.id+""}),null,16))],2)))),128))])))),128))],2),f("div",{class:"absolute w-fit bottom-[-16px] left-0 right-0 flex items-center mx-auto py-[2px] px-[6px] bg-primary rounded-t-[3px] rounded-b-[8px] text-white cursor-pointer z-[10]",onClick:ia},[f("i",{class:"block iconfont icon-shouqi text-[12px] transition-transform",style:_(`transform: scale(0.6) rotate(${sa.value?0:180}deg)`)},null,4),x(" "+N(sa.value?"收起":"展开"),1)])]),f("div",Te,[f("div",Oe,[y(l,{class:"btn !bg-page !border-fill-600",onClick:a[8]||(a[8]=e=>Na.value=!0)},{default:v((()=>a[38]||(a[38]=[x("另存为快捷查询")]))),_:1})]),y(t,{class:"w-full flex-1 h-full mx-[10px]","wrap-class":"flex items-center","view-class":"flex items-center ccc"},{default:v((()=>[(d(!0),c(h,null,b(La.value,(e=>(d(),V(l,{key:e.id,class:"group relative btn !bg-[#f5f7fa]",onClick:a=>(async e=>{Ke.value={...Ke.value,...e.infoData,queryStatus:Ke.value.queryStatus,timeBegin:`${U().subtract(6,"day").format("YYYY-MM-DD")} 00:00:00`,timeEnd:`${U().format("YYYY-MM-DD")} 23:59:59`},await Oa()})(e)},{default:v((()=>[x(N(e.infoName)+" ",1),f("div",{class:"absolute right-[-6px] top-[-6px] z-[20] cursor-pointer hidden group-hover:block",onClick:k((a=>(async e=>{if("confirm"!==await q.confirm("是否删除快捷查询按钮","删除快捷查询按钮",{confirmButtonText:"确认删除",cancelButtonText:"取消",type:"warning"}).catch((()=>"")))return;ma.value=!0;const{data:a}=await z(e).catch((()=>({})));ma.value=!1,a&&(O.success("删除成功"),await Ma())})(e.id)),["stop"])},a[39]||(a[39]=[f("i",{class:"iconfont icon-buyongtianjia text-danger text-[16px]"},null,-1)]),8,Pe)])),_:2},1032,["onClick"])))),128))])),_:1}),f("div",Be,[y(l,{class:"btn !mx-0",type:"primary",onClick:ja,text:""},{default:v((()=>a[40]||(a[40]=[f("i",{class:"iconfont icon-jianjieban-liebiaopaixu text-[14px] pr-[6px]"},null,-1),x(" 列表排序 ")]))),_:1}),y(l,{class:"btn !mx-0",type:"primary",onClick:ya,text:""},{default:v((()=>a[41]||(a[41]=[f("i",{class:"iconfont icon-xitongshezhi text-[14px] pr-[6px]"},null,-1),x(" 修改搜索条件 ")]))),_:1}),"Default"===g(Ze)?(d(),c(h,{key:0},[y(l,{class:"btn !mx-0",type:"primary",onClick:Oa},{default:v((()=>a[42]||(a[42]=[f("i",{class:"iconfont icon-sousuo text-[14px] pr-[6px]"},null,-1),x(" 搜索 ")]))),_:1}),y(l,{class:"btn !bg-page",onClick:Pa},{default:v((()=>a[43]||(a[43]=[f("i",{class:"iconfont icon-zhongzhi text-[14px] pr-[6px]"},null,-1),x(" 重置 ")]))),_:1})],64)):"Lite"===g(Ze)?(d(),c(h,{key:1},[y(l,{class:"lite-btn no-border !bg-fill-900 !text-white hover:opacity-[0.68]",onClick:Oa},{default:v((()=>a[44]||(a[44]=[f("i",{class:"iconfont icon-sousuo text-[14px] pr-[6px]"},null,-1),x(" 搜索 ")]))),_:1}),y(l,{class:"lite-btn !bg-page",onClick:Pa},{default:v((()=>a[45]||(a[45]=[f("i",{class:"iconfont icon-zhongzhi text-[14px] pr-[6px]"},null,-1),x(" 重置 ")]))),_:1})],64)):C("",!0)])]),y(i,{class:"edit-search",modelValue:da.value,"onUpdate:modelValue":a[10]||(a[10]=e=>da.value=e),size:"280","z-index":9999},{header:v((()=>a[46]||(a[46]=[f("div",{class:"text-[18px] text-fill-900 font-bold",style:{"font-family":"MicrosoftYaHei, sans-serif"}},"修改搜索条件",-1)]))),default:v((()=>[y(s,{class:"search-drag pr-[20px]",list:ca.value,onSortChange:va},{main:v((({data:e})=>[f("div",De,[y(o,{modelValue:e.show,"onUpdate:modelValue":a=>e.show=a,label:e.name,onChange:ha},null,8,["modelValue","onUpdate:modelValue","label"]),a[47]||(a[47]=f("i",{class:"iconfont icon-paixu text-[14px] text-primary"},null,-1))])])),_:1},8,["list"])])),footer:v((()=>[f("div",qe,[y(o,{modelValue:fa.value,"onUpdate:modelValue":a[9]||(a[9]=e=>fa.value=e),indeterminate:ga.value,onChange:Va,label:"全选"},null,8,["modelValue","indeterminate"]),f("div",ze,[y(l,{onClick:wa},{default:v((()=>a[48]||(a[48]=[x("恢复默认")]))),_:1}),y(l,{type:"primary",loading:ma.value,onClick:xa},{default:v((()=>a[49]||(a[49]=[x("确认")]))),_:1},8,["loading"])])])])),_:1},8,["modelValue"]),y(i,{class:"order-sort edit-search",modelValue:ba.value,"onUpdate:modelValue":a[12]||(a[12]=e=>ba.value=e),size:"280","z-index":9999},{header:v((()=>a[50]||(a[50]=[f("div",{class:"text-[18px] text-fill-900 font-bold",style:{"font-family":"MicrosoftYaHei, sans-serif"}},"列表排序",-1)]))),default:v((()=>[y(g(he),{modelValue:Sa.value,"onUpdate:modelValue":a[11]||(a[11]=e=>Sa.value=e),style:{display:"block"}},{default:v((()=>[(d(!0),c(h,null,b(g(ae),(e=>(d(),V(g(Ve),{key:e.id,label:e.id,style:{display:"block"}},{default:v((()=>[x(N(e.name),1)])),_:2},1032,["label"])))),128))])),_:1},8,["modelValue"])])),footer:v((()=>[f("div",Ue,[y(l,{type:"primary",loading:ma.value,onClick:_a},{default:v((()=>a[51]||(a[51]=[x("保存")]))),_:1},8,["loading"])])])),_:1},8,["modelValue"]),y(n,{class:"order-dialog",modelValue:Na.value,"onUpdate:modelValue":a[14]||(a[14]=e=>Na.value=e),width:"360px",title:"保存快捷查询按钮","align-center":"",draggable:"","close-on-click-modal":!1},{footer:v((()=>[f("div",Fe,[y(l,{type:"info",plain:"",onClick:Ia},{default:v((()=>a[53]||(a[53]=[x("取消")]))),_:1}),y(l,{class:"!ml-[14px]",type:"primary",loading:ma.value,disabled:!ka.value,onClick:Ta},{default:v((()=>a[54]||(a[54]=[x("确认")]))),_:1},8,["loading","disabled"])])])),default:v((()=>[f("div",Je,[a[52]||(a[52]=f("div",null,"名称",-1)),y(m,{class:"!w-[240px] ml-[12px]",modelValue:ka.value,"onUpdate:modelValue":a[13]||(a[13]=e=>ka.value=e),type:"text",maxlength:50,"show-word-limit":"",placeholder:"请输入名称"},null,8,["modelValue"])])])),_:1},8,["modelValue"]),y(n,{title:"是否导出订单信息？",modelValue:la.value,"onUpdate:modelValue":a[17]||(a[17]=e=>la.value=e),width:"300"},{footer:v((()=>[f("div",null,[y(l,{type:"info",plain:"",onClick:a[16]||(a[16]=e=>la.value=!1)},{default:v((()=>a[56]||(a[56]=[x("取消")]))),_:1}),y(l,{class:"!ml-[14px]",type:"primary",loading:ma.value,onClick:Da},{default:v((()=>a[57]||(a[57]=[x("确认")]))),_:1},8,["loading"])])])),default:v((()=>[y(o,{modelValue:g(Ke).exportSkuImg,"onUpdate:modelValue":a[15]||(a[15]=e=>g(Ke).exportSkuImg=e)},{default:v((()=>a[55]||(a[55]=[x("导出sku图片")]))),_:1},8,["modelValue"])])),_:1},8,["modelValue"]),y(Z,{visible:ta.value,"shop-id":g(Ke).shopId,shopList:g(We).platformListWidthShop,onClose:a[18]||(a[18]=e=>ta.value=!1)},null,8,["visible","shop-id","shopList"]),y(ee,{visible:oa.value,params:g(Ke),onClose:a[19]||(a[19]=e=>oa.value=!1)},null,8,["visible","params"]),y(n,{modelValue:qa.value,"onUpdate:modelValue":a[23]||(a[23]=e=>qa.value=e),width:"800px",title:"定制链接","align-center":"",draggable:"","close-on-click-modal":!1,onClose:Ea},{footer:v((()=>[f("div",Re,[y(l,{type:"primary",onClick:a[22]||(a[22]=e=>qa.value=!1)},{default:v((()=>a[65]||(a[65]=[x("关闭")]))),_:1})])])),default:v((()=>[f("div",Ee,[f("div",null,[f("div",Ye,[y(g(L),{color:"#f9cc9d"},{default:v((()=>[y(M)])),_:1}),a[58]||(a[58]=x("可以输入买家订单号，会自动将订单号带入链接中，买家可以直接定制；"))]),a[59]||(a[59]=f("div",{class:"leading-8"},"或者直接将定制链接发给买家，需要买家自行输入订单号",-1))]),f("div",Ae,[f("div",null,[a[61]||(a[61]=x(" 订单号: ")),y(m,{class:"!w-[240px] ml-[12px]",modelValue:za.value,"onUpdate:modelValue":a[20]||(a[20]=e=>za.value=e),onKeyup:I(Fa,["enter"]),placeholder:"卖家订单号"},null,8,["modelValue"]),a[62]||(a[62]=x()),y(l,{class:"ml-[4px]",type:"primary",onClick:Fa},{default:v((()=>a[60]||(a[60]=[x("按回车生成链接")]))),_:1})]),f("div",He,[a[63]||(a[63]=x(" 定制链接： ")),f("span",null,N(Ja.value),1),a[64]||(a[64]=x()),f("i",{onClick:a[21]||(a[21]=e=>(async(e,a)=>{await J(e).catch((()=>({})))?O.success(`${a}复制成功`):O.warning("复制失败")})(Ja.value,"定制链接")),class:"ml-[10px] iconfont icon-fuzhidingdan text-[12px]"})])])])])),_:1},8,["modelValue"])])),[[P,ma.value]])}}}),[["__scopeId","data-v-76b09359"]]);export{Ge as default};
