import type { TSESTree } from '@typescript-eslint/utils';
import type { InferMessageIdsTypeFromRule, InferOptionsTypeFromRule } from '../util';
declare const baseRule: import("@typescript-eslint/utils/ts-eslint").RuleModule<"initialized" | "notInitialized", ["always" | "never", ({
    ignoreForLoopInit?: boolean;
} | undefined)?], unknown, {
    'VariableDeclaration:exit'(node: TSESTree.VariableDeclaration): void;
}>;
export type Options = InferOptionsTypeFromRule<typeof baseRule>;
export type MessageIds = InferMessageIdsTypeFromRule<typeof baseRule>;
declare const _default: import("@typescript-eslint/utils/ts-eslint").RuleModule<"initialized" | "notInitialized", ["always" | "never", ({
    ignoreForLoopInit?: boolean;
} | undefined)?], import("../../rules").ESLintPluginDocs, import("@typescript-eslint/utils/ts-eslint").RuleListener>;
export default _default;
//# sourceMappingURL=init-declarations.d.ts.map