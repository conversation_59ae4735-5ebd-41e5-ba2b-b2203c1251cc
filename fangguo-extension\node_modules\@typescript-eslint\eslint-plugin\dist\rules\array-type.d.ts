export type OptionString = 'array' | 'array-simple' | 'generic';
export type Options = [
    {
        default: OptionString;
        readonly?: OptionString;
    }
];
export type MessageIds = 'errorStringArray' | 'errorStringArrayReadonly' | 'errorStringArraySimple' | 'errorStringArraySimpleReadonly' | 'errorStringGeneric' | 'errorStringGenericSimple';
declare const _default: import("@typescript-eslint/utils/ts-eslint").RuleModule<MessageIds, Options, import("../../rules").ESLintPluginDocs, import("@typescript-eslint/utils/ts-eslint").RuleListener>;
export default _default;
//# sourceMappingURL=array-type.d.ts.map