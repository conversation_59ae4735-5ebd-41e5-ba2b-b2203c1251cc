import{d as e,r as a,w as l,g as t,c as o,y as u,ce as s,v as d,b as n,z as p,f as i,x as m,aV as r,Q as c,a6 as v,_ as f}from"./index-PITnHt5Y.js";/* empty css              */const y={class:"w-full"},V={key:0},x={class:"pt-[24px] pb-[12px]"},_=f(e({__name:"CustomMultipleInput",props:{name:{type:String,default:"请输入"},modelValue:{type:String,required:!0},multiple:{type:Boolean,default:!0},clear:{type:Boolean,default:!0}},emits:["update:modelValue","enter"],setup(e,{emit:f}){const _=e,g=f,k=a("");l((()=>_.modelValue),(e=>k.value=e),{immediate:!0});const w=e=>g("update:modelValue",e),h=a(""),C=a(!1),b=()=>{h.value=k.value,C.value=!0},R=()=>{g("update:modelValue",h.value),C.value=!1},U=()=>g("enter");return(a,l)=>{const f=r,_=c,g=v;return t(),o("div",y,[u(f,{class:"custom-input",modelValue:k.value,"onUpdate:modelValue":l[0]||(l[0]=e=>k.value=e),placeholder:`${e.name}${e.multiple?"，多个用逗号或空格分隔":""}`,clearable:"",onChange:w,onKeydown:p(U,["enter"])},s({_:2},[e.multiple?{name:"append",fn:d((()=>[n("span",{onClick:b},"•••")])),key:"0"}:void 0]),1032,["modelValue","placeholder"]),e.multiple?(t(),o("div",V,[u(g,{class:"order-dialog",modelValue:C.value,"onUpdate:modelValue":l[3]||(l[3]=e=>C.value=e),width:"480px",title:e.name,"align-center":"",draggable:"","close-on-click-modal":!1},{footer:d((()=>[n("div",x,[u(_,{type:"info",plain:"",onClick:l[2]||(l[2]=e=>C.value=!1)},{default:d((()=>l[4]||(l[4]=[i("取消")]))),_:1}),u(_,{class:"!ml-[14px]",type:"primary",onClick:R},{default:d((()=>l[5]||(l[5]=[i("确认")]))),_:1})])])),default:d((()=>[l[6]||(l[6]=n("div",{class:"py-[12px] text-fill-900",style:{"font-family":"MicrosoftYaHei, sans-serif"}}," 多个值以逗号隔开或空格隔开 ",-1)),u(f,{modelValue:h.value,"onUpdate:modelValue":l[1]||(l[1]=e=>h.value=e),type:"textarea",autosize:{minRows:3,maxRows:5},placeholder:"多个用逗号或空格分隔"},null,8,["modelValue"])])),_:1},8,["modelValue","title"])])):m("",!0)])}}}),[["__scopeId","data-v-1cee9829"]]);export{_};
