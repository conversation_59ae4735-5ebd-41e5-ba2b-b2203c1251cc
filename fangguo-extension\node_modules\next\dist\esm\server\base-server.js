import { getFallbackRouteParams } from './request/fallback-params';
import { CachedRouteKind } from './response-cache';
import { NormalizeError, DecodeError, normalizeRepeatedSlashes, MissingStaticPage } from '../shared/lib/utils';
import { format as formatUrl, parse as parseUrl } from 'url';
import { formatHostname } from './lib/format-hostname';
import { getRedirectStatus } from '../lib/redirect-status';
import { isEdgeRuntime } from '../lib/is-edge-runtime';
import { APP_PATHS_MANIFEST, NEXT_BUILTIN_DOCUMENT, PAGES_MANIFEST, STATIC_STATUS_PAGES, UNDERSCORE_NOT_FOUND_ROUTE, UNDERSCORE_NOT_FOUND_ROUTE_ENTRY } from '../shared/lib/constants';
import { isDynamicRoute } from '../shared/lib/router/utils';
import { checkIsOnDemandRevalidate } from './api-utils';
import { setConfig } from '../shared/lib/runtime-config.external';
import { getCacheControlHeader } from './lib/cache-control';
import { execOnce } from '../shared/lib/utils';
import { isBlockedPage } from './utils';
import { getBotType, isBot } from '../shared/lib/router/utils/is-bot';
import RenderResult from './render-result';
import { removeTrailingSlash } from '../shared/lib/router/utils/remove-trailing-slash';
import { denormalizePagePath } from '../shared/lib/page-path/denormalize-page-path';
import * as Log from '../build/output/log';
import { getPreviouslyRevalidatedTags, getServerUtils } from './server-utils';
import isError, { getProperError } from '../lib/is-error';
import { addRequestMeta, getRequestMeta, removeRequestMeta, setRequestMeta } from './request-meta';
import { removePathPrefix } from '../shared/lib/router/utils/remove-path-prefix';
import { normalizeAppPath } from '../shared/lib/router/utils/app-paths';
import { getHostname } from '../shared/lib/get-hostname';
import { parseUrl as parseUrlUtil } from '../shared/lib/router/utils/parse-url';
import { getNextPathnameInfo } from '../shared/lib/router/utils/get-next-pathname-info';
import { RSC_HEADER, NEXT_RSC_UNION_QUERY, NEXT_ROUTER_PREFETCH_HEADER, NEXT_ROUTER_SEGMENT_PREFETCH_HEADER, NEXT_DID_POSTPONE_HEADER, NEXT_URL, NEXT_ROUTER_STATE_TREE_HEADER, NEXT_IS_PRERENDER_HEADER } from '../client/components/app-router-headers';
import { LocaleRouteNormalizer } from './normalizers/locale-route-normalizer';
import { DefaultRouteMatcherManager } from './route-matcher-managers/default-route-matcher-manager';
import { AppPageRouteMatcherProvider } from './route-matcher-providers/app-page-route-matcher-provider';
import { AppRouteRouteMatcherProvider } from './route-matcher-providers/app-route-route-matcher-provider';
import { PagesAPIRouteMatcherProvider } from './route-matcher-providers/pages-api-route-matcher-provider';
import { PagesRouteMatcherProvider } from './route-matcher-providers/pages-route-matcher-provider';
import { ServerManifestLoader } from './route-matcher-providers/helpers/manifest-loaders/server-manifest-loader';
import { getTracer, isBubbledError, SpanKind } from './lib/trace/tracer';
import { BaseServerSpan } from './lib/trace/constants';
import { I18NProvider } from './lib/i18n-provider';
import { sendResponse } from './send-response';
import { normalizeNextQueryParam } from './web/utils';
import { CACHE_ONE_YEAR, MATCHED_PATH_HEADER, NEXT_CACHE_TAGS_HEADER, NEXT_RESUME_HEADER } from '../lib/constants';
import { normalizeLocalePath } from '../shared/lib/i18n/normalize-locale-path';
import { matchNextDataPathname } from './lib/match-next-data-pathname';
import getRouteFromAssetPath from '../shared/lib/router/utils/get-route-from-asset-path';
import { decodePathParams } from './lib/router-utils/decode-path-params';
import { RSCPathnameNormalizer } from './normalizers/request/rsc';
import { stripFlightHeaders } from './app-render/strip-flight-headers';
import { isAppPageRouteModule, isAppRouteRouteModule, isPagesRouteModule } from './route-modules/checks';
import { PrefetchRSCPathnameNormalizer } from './normalizers/request/prefetch-rsc';
import { NextDataPathnameNormalizer } from './normalizers/request/next-data';
import { getIsPossibleServerAction } from './lib/server-action-request-meta';
import { isInterceptionRouteAppPath } from '../shared/lib/router/utils/interception-routes';
import { toRoute } from './lib/to-route';
import { isNodeNextRequest, isNodeNextResponse } from './base-http/helpers';
import { patchSetHeaderWithCookieSupport } from './lib/patch-set-header';
import { checkIsAppPPREnabled } from './lib/experimental/ppr';
import { getBuiltinRequestContext } from './after/builtin-request-context';
import { ENCODED_TAGS } from './stream-utils/encoded-tags';
import { NextRequestHint } from './web/adapter';
import { getRevalidateReason } from './instrumentation/utils';
import { RouteKind } from './route-kind';
import { FallbackMode, parseFallbackField } from '../lib/fallback';
import { toResponseCacheEntry } from './response-cache/utils';
import { SegmentPrefixRSCPathnameNormalizer } from './normalizers/request/segment-prefix-rsc';
import { shouldServeStreamingMetadata, isHtmlBotRequest } from './lib/streaming-metadata';
import { InvariantError } from '../shared/lib/invariant-error';
import { decodeQueryPathParameter } from './lib/decode-query-path-parameter';
import { NoFallbackError } from '../shared/lib/no-fallback-error.external';
import { getCacheHandlers } from './use-cache/handlers';
import { fixMojibake } from './lib/fix-mojibake';
import { computeCacheBustingSearchParam } from '../shared/lib/router/utils/cache-busting-search-param';
import { RedirectStatusCode } from '../client/components/redirect-status-code';
import { setCacheBustingSearchParamWithHash } from '../client/components/router-reducer/set-cache-busting-search-param';
// Internal wrapper around build errors at development
// time, to prevent us from propagating or logging them
export class WrappedBuildError extends Error {
    constructor(innerError){
        super();
        this.innerError = innerError;
    }
}
export default class Server {
    getServerComponentsHmrCache() {
        return this.nextConfig.experimental.serverComponentsHmrCache ? globalThis.__serverComponentsHmrCache : undefined;
    }
    /**
   * This is used to persist cache scopes across
   * prefetch -> full route requests for dynamic IO
   * it's only fully used in dev
   */ constructor(options){
        var _this_nextConfig_i18n, _this_nextConfig_experimental_amp, _this_nextConfig_i18n1;
        this.handleRSCRequest = (req, _res, parsedUrl)=>{
            var _this_normalizers_segmentPrefetchRSC, _this_normalizers_prefetchRSC, _this_normalizers_rsc;
            if (!parsedUrl.pathname) return false;
            if ((_this_normalizers_segmentPrefetchRSC = this.normalizers.segmentPrefetchRSC) == null ? void 0 : _this_normalizers_segmentPrefetchRSC.match(parsedUrl.pathname)) {
                const result = this.normalizers.segmentPrefetchRSC.extract(parsedUrl.pathname);
                if (!result) return false;
                const { originalPathname, segmentPath } = result;
                parsedUrl.pathname = originalPathname;
                // Mark the request as a router prefetch request.
                req.headers[RSC_HEADER.toLowerCase()] = '1';
                req.headers[NEXT_ROUTER_PREFETCH_HEADER.toLowerCase()] = '1';
                req.headers[NEXT_ROUTER_SEGMENT_PREFETCH_HEADER.toLowerCase()] = segmentPath;
                addRequestMeta(req, 'isRSCRequest', true);
                addRequestMeta(req, 'isPrefetchRSCRequest', true);
                addRequestMeta(req, 'segmentPrefetchRSCRequest', segmentPath);
            } else if ((_this_normalizers_prefetchRSC = this.normalizers.prefetchRSC) == null ? void 0 : _this_normalizers_prefetchRSC.match(parsedUrl.pathname)) {
                parsedUrl.pathname = this.normalizers.prefetchRSC.normalize(parsedUrl.pathname, true);
                // Mark the request as a router prefetch request.
                req.headers[RSC_HEADER.toLowerCase()] = '1';
                req.headers[NEXT_ROUTER_PREFETCH_HEADER.toLowerCase()] = '1';
                addRequestMeta(req, 'isRSCRequest', true);
                addRequestMeta(req, 'isPrefetchRSCRequest', true);
            } else if ((_this_normalizers_rsc = this.normalizers.rsc) == null ? void 0 : _this_normalizers_rsc.match(parsedUrl.pathname)) {
                parsedUrl.pathname = this.normalizers.rsc.normalize(parsedUrl.pathname, true);
                // Mark the request as a RSC request.
                req.headers[RSC_HEADER.toLowerCase()] = '1';
                addRequestMeta(req, 'isRSCRequest', true);
            } else if (req.headers['x-now-route-matches']) {
                // If we didn't match, return with the flight headers stripped. If in
                // minimal mode we didn't match based on the path, this can't be a RSC
                // request. This is because Vercel only sends this header during
                // revalidation requests and we want the cache to instead depend on the
                // request path for flight information.
                stripFlightHeaders(req.headers);
                return false;
            } else if (req.headers[RSC_HEADER.toLowerCase()] === '1') {
                addRequestMeta(req, 'isRSCRequest', true);
                if (req.headers[NEXT_ROUTER_PREFETCH_HEADER.toLowerCase()] === '1') {
                    addRequestMeta(req, 'isPrefetchRSCRequest', true);
                    const segmentPrefetchRSCRequest = req.headers[NEXT_ROUTER_SEGMENT_PREFETCH_HEADER.toLowerCase()];
                    if (typeof segmentPrefetchRSCRequest === 'string') {
                        addRequestMeta(req, 'segmentPrefetchRSCRequest', segmentPrefetchRSCRequest);
                    }
                }
            } else {
                // Otherwise just return without doing anything.
                return false;
            }
            if (req.url) {
                const parsed = parseUrl(req.url);
                parsed.pathname = parsedUrl.pathname;
                req.url = formatUrl(parsed);
            }
            return false;
        };
        this.handleNextDataRequest = async (req, res, parsedUrl)=>{
            const middleware = await this.getMiddleware();
            const params = matchNextDataPathname(parsedUrl.pathname);
            // ignore for non-next data URLs
            if (!params || !params.path) {
                return false;
            }
            if (params.path[0] !== this.buildId) {
                // Ignore if its a middleware request when we aren't on edge.
                if (process.env.NEXT_RUNTIME !== 'edge' && getRequestMeta(req, 'middlewareInvoke')) {
                    return false;
                }
                // Make sure to 404 if the buildId isn't correct
                await this.render404(req, res, parsedUrl);
                return true;
            }
            // remove buildId from URL
            params.path.shift();
            const lastParam = params.path[params.path.length - 1];
            // show 404 if it doesn't end with .json
            if (typeof lastParam !== 'string' || !lastParam.endsWith('.json')) {
                await this.render404(req, res, parsedUrl);
                return true;
            }
            // re-create page's pathname
            let pathname = `/${params.path.join('/')}`;
            pathname = getRouteFromAssetPath(pathname, '.json');
            // ensure trailing slash is normalized per config
            if (middleware) {
                if (this.nextConfig.trailingSlash && !pathname.endsWith('/')) {
                    pathname += '/';
                }
                if (!this.nextConfig.trailingSlash && pathname.length > 1 && pathname.endsWith('/')) {
                    pathname = pathname.substring(0, pathname.length - 1);
                }
            }
            if (this.i18nProvider) {
                var _req_headers_host;
                // Remove the port from the hostname if present.
                const hostname = req == null ? void 0 : (_req_headers_host = req.headers.host) == null ? void 0 : _req_headers_host.split(':', 1)[0].toLowerCase();
                const domainLocale = this.i18nProvider.detectDomainLocale(hostname);
                const defaultLocale = (domainLocale == null ? void 0 : domainLocale.defaultLocale) ?? this.i18nProvider.config.defaultLocale;
                const localePathResult = this.i18nProvider.analyze(pathname);
                // If the locale is detected from the path, we need to remove it
                // from the pathname.
                if (localePathResult.detectedLocale) {
                    pathname = localePathResult.pathname;
                }
                // Update the query with the detected locale and default locale.
                addRequestMeta(req, 'locale', localePathResult.detectedLocale);
                addRequestMeta(req, 'defaultLocale', defaultLocale);
                // If the locale is not detected from the path, we need to mark that
                // it was not inferred from default.
                if (!localePathResult.detectedLocale) {
                    removeRequestMeta(req, 'localeInferredFromDefault');
                }
                // If no locale was detected and we don't have middleware, we need
                // to render a 404 page.
                if (!localePathResult.detectedLocale && !middleware) {
                    addRequestMeta(req, 'locale', defaultLocale);
                    await this.render404(req, res, parsedUrl);
                    return true;
                }
            }
            parsedUrl.pathname = pathname;
            addRequestMeta(req, 'isNextDataReq', true);
            return false;
        };
        this.handleNextImageRequest = ()=>false;
        this.handleCatchallRenderRequest = ()=>false;
        this.handleCatchallMiddlewareRequest = ()=>false;
        /**
   * Normalizes a pathname without attaching any metadata from any matched
   * normalizer.
   *
   * @param pathname the pathname to normalize
   * @returns the normalized pathname
   */ this.normalize = (pathname)=>{
            const normalizers = [];
            if (this.normalizers.data) {
                normalizers.push(this.normalizers.data);
            }
            // We have to put the segment prefetch normalizer before the RSC normalizer
            // because the RSC normalizer will match the prefetch RSC routes too.
            if (this.normalizers.segmentPrefetchRSC) {
                normalizers.push(this.normalizers.segmentPrefetchRSC);
            }
            // We have to put the prefetch normalizer before the RSC normalizer
            // because the RSC normalizer will match the prefetch RSC routes too.
            if (this.normalizers.prefetchRSC) {
                normalizers.push(this.normalizers.prefetchRSC);
            }
            if (this.normalizers.rsc) {
                normalizers.push(this.normalizers.rsc);
            }
            for (const normalizer of normalizers){
                if (!normalizer.match(pathname)) continue;
                return normalizer.normalize(pathname, true);
            }
            return pathname;
        };
        this.normalizeAndAttachMetadata = async (req, res, url)=>{
            let finished = await this.handleNextImageRequest(req, res, url);
            if (finished) return true;
            if (this.enabledDirectories.pages) {
                finished = await this.handleNextDataRequest(req, res, url);
                if (finished) return true;
            }
            return false;
        };
        this.prepared = false;
        this.preparedPromise = null;
        this.customErrorNo404Warn = execOnce(()=>{
            Log.warn(`You have added a custom /_error page without a custom /404 page. This prevents the 404 page from being auto statically optimized.\nSee here for info: https://nextjs.org/docs/messages/custom-error-no-custom-404`);
        });
        const { dir = '.', quiet = false, conf, dev = false, minimalMode = false, hostname, port, experimentalTestProxy } = options;
        this.experimentalTestProxy = experimentalTestProxy;
        this.serverOptions = options;
        this.dir = process.env.NEXT_RUNTIME === 'edge' ? dir : require('path').resolve(dir);
        this.quiet = quiet;
        this.loadEnvConfig({
            dev
        });
        // TODO: should conf be normalized to prevent missing
        // values from causing issues as this can be user provided
        this.nextConfig = conf;
        this.hostname = hostname;
        if (this.hostname) {
            // we format the hostname so that it can be fetched
            this.fetchHostname = formatHostname(this.hostname);
        }
        this.port = port;
        this.distDir = process.env.NEXT_RUNTIME === 'edge' ? this.nextConfig.distDir : require('path').join(this.dir, this.nextConfig.distDir);
        this.publicDir = this.getPublicDir();
        this.hasStaticDir = !minimalMode && this.getHasStaticDir();
        this.i18nProvider = ((_this_nextConfig_i18n = this.nextConfig.i18n) == null ? void 0 : _this_nextConfig_i18n.locales) ? new I18NProvider(this.nextConfig.i18n) : undefined;
        // Configure the locale normalizer, it's used for routes inside `pages/`.
        this.localeNormalizer = this.i18nProvider ? new LocaleRouteNormalizer(this.i18nProvider) : undefined;
        // Only serverRuntimeConfig needs the default
        // publicRuntimeConfig gets it's default in client/index.js
        const { serverRuntimeConfig = {}, publicRuntimeConfig, assetPrefix, generateEtags } = this.nextConfig;
        this.buildId = this.getBuildId();
        // this is a hack to avoid Webpack knowing this is equal to this.minimalMode
        // because we replace this.minimalMode to true in production bundles.
        const minimalModeKey = 'minimalMode';
        this[minimalModeKey] = minimalMode || !!process.env.NEXT_PRIVATE_MINIMAL_MODE;
        this.enabledDirectories = this.getEnabledDirectories(dev);
        this.isAppPPREnabled = this.enabledDirectories.app && checkIsAppPPREnabled(this.nextConfig.experimental.ppr);
        this.isAppSegmentPrefetchEnabled = this.enabledDirectories.app && this.nextConfig.experimental.clientSegmentCache === true;
        this.normalizers = {
            // We should normalize the pathname from the RSC prefix only in minimal
            // mode as otherwise that route is not exposed external to the server as
            // we instead only rely on the headers.
            rsc: this.enabledDirectories.app && this.minimalMode ? new RSCPathnameNormalizer() : undefined,
            prefetchRSC: this.isAppPPREnabled && this.minimalMode ? new PrefetchRSCPathnameNormalizer() : undefined,
            segmentPrefetchRSC: this.isAppSegmentPrefetchEnabled && this.minimalMode ? new SegmentPrefixRSCPathnameNormalizer() : undefined,
            data: this.enabledDirectories.pages ? new NextDataPathnameNormalizer(this.buildId) : undefined
        };
        this.nextFontManifest = this.getNextFontManifest();
        if (process.env.NEXT_RUNTIME !== 'edge') {
            process.env.NEXT_DEPLOYMENT_ID = this.nextConfig.deploymentId || '';
        }
        this.renderOpts = {
            dir: this.dir,
            supportsDynamicResponse: true,
            trailingSlash: this.nextConfig.trailingSlash,
            deploymentId: this.nextConfig.deploymentId,
            strictNextHead: this.nextConfig.experimental.strictNextHead ?? true,
            poweredByHeader: this.nextConfig.poweredByHeader,
            canonicalBase: this.nextConfig.amp.canonicalBase || '',
            generateEtags,
            previewProps: this.getPrerenderManifest().preview,
            ampOptimizerConfig: (_this_nextConfig_experimental_amp = this.nextConfig.experimental.amp) == null ? void 0 : _this_nextConfig_experimental_amp.optimizer,
            basePath: this.nextConfig.basePath,
            images: this.nextConfig.images,
            optimizeCss: this.nextConfig.experimental.optimizeCss,
            nextConfigOutput: this.nextConfig.output,
            nextScriptWorkers: this.nextConfig.experimental.nextScriptWorkers,
            disableOptimizedLoading: this.nextConfig.experimental.disableOptimizedLoading,
            domainLocales: (_this_nextConfig_i18n1 = this.nextConfig.i18n) == null ? void 0 : _this_nextConfig_i18n1.domains,
            distDir: this.distDir,
            serverComponents: this.enabledDirectories.app,
            cacheLifeProfiles: this.nextConfig.experimental.cacheLife,
            enableTainting: this.nextConfig.experimental.taint,
            crossOrigin: this.nextConfig.crossOrigin ? this.nextConfig.crossOrigin : undefined,
            largePageDataBytes: this.nextConfig.experimental.largePageDataBytes,
            // Only the `publicRuntimeConfig` key is exposed to the client side
            // It'll be rendered as part of __NEXT_DATA__ on the client side
            runtimeConfig: Object.keys(publicRuntimeConfig).length > 0 ? publicRuntimeConfig : undefined,
            isExperimentalCompile: this.nextConfig.experimental.isExperimentalCompile,
            // `htmlLimitedBots` is passed to server as serialized config in string format
            htmlLimitedBots: this.nextConfig.htmlLimitedBots,
            experimental: {
                expireTime: this.nextConfig.expireTime,
                staleTimes: this.nextConfig.experimental.staleTimes,
                clientTraceMetadata: this.nextConfig.experimental.clientTraceMetadata,
                dynamicIO: this.nextConfig.experimental.dynamicIO ?? false,
                clientSegmentCache: this.nextConfig.experimental.clientSegmentCache === 'client-only' ? 'client-only' : Boolean(this.nextConfig.experimental.clientSegmentCache),
                dynamicOnHover: this.nextConfig.experimental.dynamicOnHover ?? false,
                inlineCss: this.nextConfig.experimental.inlineCss ?? false,
                authInterrupts: !!this.nextConfig.experimental.authInterrupts
            },
            onInstrumentationRequestError: this.instrumentationOnRequestError.bind(this),
            reactMaxHeadersLength: this.nextConfig.reactMaxHeadersLength,
            devtoolSegmentExplorer: this.nextConfig.experimental.devtoolSegmentExplorer
        };
        // Initialize next/config with the environment configuration
        setConfig({
            serverRuntimeConfig,
            publicRuntimeConfig
        });
        this.pagesManifest = this.getPagesManifest();
        this.appPathsManifest = this.getAppPathsManifest();
        this.appPathRoutes = this.getAppPathRoutes();
        this.interceptionRoutePatterns = this.getinterceptionRoutePatterns();
        // Configure the routes.
        this.matchers = this.getRouteMatchers();
        // Start route compilation. We don't wait for the routes to finish loading
        // because we use the `waitTillReady` promise below in `handleRequest` to
        // wait. Also we can't `await` in the constructor.
        void this.matchers.reload();
        this.setAssetPrefix(assetPrefix);
        this.responseCache = this.getResponseCache({
            dev
        });
    }
    reloadMatchers() {
        return this.matchers.reload();
    }
    getRouteMatchers() {
        // Create a new manifest loader that get's the manifests from the server.
        const manifestLoader = new ServerManifestLoader((name)=>{
            switch(name){
                case PAGES_MANIFEST:
                    return this.getPagesManifest() ?? null;
                case APP_PATHS_MANIFEST:
                    return this.getAppPathsManifest() ?? null;
                default:
                    return null;
            }
        });
        // Configure the matchers and handlers.
        const matchers = new DefaultRouteMatcherManager();
        // Match pages under `pages/`.
        matchers.push(new PagesRouteMatcherProvider(this.distDir, manifestLoader, this.i18nProvider));
        // Match api routes under `pages/api/`.
        matchers.push(new PagesAPIRouteMatcherProvider(this.distDir, manifestLoader, this.i18nProvider));
        // If the app directory is enabled, then add the app matchers and handlers.
        if (this.enabledDirectories.app) {
            // Match app pages under `app/`.
            matchers.push(new AppPageRouteMatcherProvider(this.distDir, manifestLoader));
            matchers.push(new AppRouteRouteMatcherProvider(this.distDir, manifestLoader));
        }
        return matchers;
    }
    async instrumentationOnRequestError(...args) {
        const [err, req, ctx] = args;
        if (this.instrumentation) {
            try {
                await (this.instrumentation.onRequestError == null ? void 0 : this.instrumentation.onRequestError.call(this.instrumentation, err, {
                    path: req.url || '',
                    method: req.method || 'GET',
                    // Normalize middleware headers and other server request headers
                    headers: req instanceof NextRequestHint ? Object.fromEntries(req.headers.entries()) : req.headers
                }, ctx));
            } catch (handlerErr) {
                // Log the soft error and continue, since errors can thrown from react stream handler
                console.error('Error in instrumentation.onRequestError:', handlerErr);
            }
        }
    }
    logError(err) {
        if (this.quiet) return;
        Log.error(err);
    }
    async handleRequest(req, res, parsedUrl) {
        await this.prepare();
        const method = req.method.toUpperCase();
        const tracer = getTracer();
        return tracer.withPropagatedContext(req.headers, ()=>{
            return tracer.trace(BaseServerSpan.handleRequest, {
                spanName: `${method} ${req.url}`,
                kind: SpanKind.SERVER,
                attributes: {
                    'http.method': method,
                    'http.target': req.url
                }
            }, async (span)=>this.handleRequestImpl(req, res, parsedUrl).finally(()=>{
                    if (!span) return;
                    const isRSCRequest = getRequestMeta(req, 'isRSCRequest') ?? false;
                    span.setAttributes({
                        'http.status_code': res.statusCode,
                        'next.rsc': isRSCRequest
                    });
                    const rootSpanAttributes = tracer.getRootSpanAttributes();
                    // We were unable to get attributes, probably OTEL is not enabled
                    if (!rootSpanAttributes) return;
                    if (rootSpanAttributes.get('next.span_type') !== BaseServerSpan.handleRequest) {
                        console.warn(`Unexpected root span type '${rootSpanAttributes.get('next.span_type')}'. Please report this Next.js issue https://github.com/vercel/next.js`);
                        return;
                    }
                    const route = rootSpanAttributes.get('next.route');
                    if (route) {
                        const name = isRSCRequest ? `RSC ${method} ${route}` : `${method} ${route}`;
                        span.setAttributes({
                            'next.route': route,
                            'http.route': route,
                            'next.span_name': name
                        });
                        span.updateName(name);
                    } else {
                        span.updateName(isRSCRequest ? `RSC ${method} ${req.url}` : `${method} ${req.url}`);
                    }
                }));
        });
    }
    async handleRequestImpl(req, res, parsedUrl) {
        try {
            var _originalRequest_socket, _originalRequest_socket1, _this_i18nProvider, _this_nextConfig_i18n;
            // Wait for the matchers to be ready.
            await this.matchers.waitTillReady();
            // ensure cookies set in middleware are merged and
            // not overridden by API routes/getServerSideProps
            patchSetHeaderWithCookieSupport(req, isNodeNextResponse(res) ? res.originalResponse : res);
            const urlParts = (req.url || '').split('?', 1);
            const urlNoQuery = urlParts[0];
            // this normalizes repeated slashes in the path e.g. hello//world ->
            // hello/world or backslashes to forward slashes, this does not
            // handle trailing slash as that is handled the same as a next.config.js
            // redirect
            if (urlNoQuery == null ? void 0 : urlNoQuery.match(/(\\|\/\/)/)) {
                const cleanUrl = normalizeRepeatedSlashes(req.url);
                res.redirect(cleanUrl, 308).body(cleanUrl).send();
                return;
            }
            // Parse url if parsedUrl not provided
            if (!parsedUrl || typeof parsedUrl !== 'object') {
                if (!req.url) {
                    throw Object.defineProperty(new Error('Invariant: url can not be undefined'), "__NEXT_ERROR_CODE", {
                        value: "E123",
                        enumerable: false,
                        configurable: true
                    });
                }
                parsedUrl = parseUrl(req.url, true);
            }
            if (!parsedUrl.pathname) {
                throw Object.defineProperty(new Error("Invariant: pathname can't be empty"), "__NEXT_ERROR_CODE", {
                    value: "E412",
                    enumerable: false,
                    configurable: true
                });
            }
            // Parse the querystring ourselves if the user doesn't handle querystring parsing
            if (typeof parsedUrl.query === 'string') {
                parsedUrl.query = Object.fromEntries(new URLSearchParams(parsedUrl.query));
            }
            // Update the `x-forwarded-*` headers.
            const { originalRequest = null } = isNodeNextRequest(req) ? req : {};
            const xForwardedProto = originalRequest == null ? void 0 : originalRequest.headers['x-forwarded-proto'];
            const isHttps = xForwardedProto ? xForwardedProto === 'https' : !!(originalRequest == null ? void 0 : (_originalRequest_socket = originalRequest.socket) == null ? void 0 : _originalRequest_socket.encrypted);
            req.headers['x-forwarded-host'] ??= req.headers['host'] ?? this.hostname;
            req.headers['x-forwarded-port'] ??= this.port ? this.port.toString() : isHttps ? '443' : '80';
            req.headers['x-forwarded-proto'] ??= isHttps ? 'https' : 'http';
            req.headers['x-forwarded-for'] ??= originalRequest == null ? void 0 : (_originalRequest_socket1 = originalRequest.socket) == null ? void 0 : _originalRequest_socket1.remoteAddress;
            // This should be done before any normalization of the pathname happens as
            // it captures the initial URL.
            this.attachRequestMeta(req, parsedUrl);
            let finished = await this.handleRSCRequest(req, res, parsedUrl);
            if (finished) return;
            const domainLocale = (_this_i18nProvider = this.i18nProvider) == null ? void 0 : _this_i18nProvider.detectDomainLocale(getHostname(parsedUrl, req.headers));
            const defaultLocale = (domainLocale == null ? void 0 : domainLocale.defaultLocale) || ((_this_nextConfig_i18n = this.nextConfig.i18n) == null ? void 0 : _this_nextConfig_i18n.defaultLocale);
            addRequestMeta(req, 'defaultLocale', defaultLocale);
            const url = parseUrlUtil(req.url.replace(/^\/+/, '/'));
            const pathnameInfo = getNextPathnameInfo(url.pathname, {
                nextConfig: this.nextConfig,
                i18nProvider: this.i18nProvider
            });
            url.pathname = pathnameInfo.pathname;
            if (pathnameInfo.basePath) {
                req.url = removePathPrefix(req.url, this.nextConfig.basePath);
            }
            const useMatchedPathHeader = this.minimalMode && typeof req.headers[MATCHED_PATH_HEADER] === 'string';
            // TODO: merge handling with invokePath
            if (useMatchedPathHeader) {
                try {
                    var _this_normalizers_data, _this_i18nProvider1, _this_getRoutesManifest;
                    if (this.enabledDirectories.app) {
                        // ensure /index path is normalized for prerender
                        // in minimal mode
                        if (req.url.match(/^\/index($|\?)/)) {
                            req.url = req.url.replace(/^\/index/, '/');
                        }
                        parsedUrl.pathname = parsedUrl.pathname === '/index' ? '/' : parsedUrl.pathname;
                    }
                    // x-matched-path is the source of truth, it tells what page
                    // should be rendered because we don't process rewrites in minimalMode
                    let { pathname: matchedPath } = new URL(fixMojibake(req.headers[MATCHED_PATH_HEADER]), 'http://localhost');
                    let { pathname: urlPathname } = new URL(req.url, 'http://localhost');
                    // For ISR the URL is normalized to the prerenderPath so if
                    // it's a data request the URL path will be the data URL,
                    // basePath is already stripped by this point
                    if ((_this_normalizers_data = this.normalizers.data) == null ? void 0 : _this_normalizers_data.match(urlPathname)) {
                        addRequestMeta(req, 'isNextDataReq', true);
                    } else if (this.isAppPPREnabled && this.minimalMode && req.headers[NEXT_RESUME_HEADER] === '1' && req.method === 'POST') {
                        // Decode the postponed state from the request body, it will come as
                        // an array of buffers, so collect them and then concat them to form
                        // the string.
                        const body = [];
                        for await (const chunk of req.body){
                            body.push(chunk);
                        }
                        const postponed = Buffer.concat(body).toString('utf8');
                        addRequestMeta(req, 'postponed', postponed);
                    }
                    matchedPath = this.normalize(matchedPath);
                    const normalizedUrlPath = this.stripNextDataPath(urlPathname);
                    matchedPath = denormalizePagePath(matchedPath);
                    // Perform locale detection and normalization.
                    const localeAnalysisResult = (_this_i18nProvider1 = this.i18nProvider) == null ? void 0 : _this_i18nProvider1.analyze(matchedPath, {
                        defaultLocale
                    });
                    // The locale result will be defined even if the locale was not
                    // detected for the request because it will be inferred from the
                    // default locale.
                    if (localeAnalysisResult) {
                        addRequestMeta(req, 'locale', localeAnalysisResult.detectedLocale);
                        // If the detected locale was inferred from the default locale, we
                        // need to modify the metadata on the request to indicate that.
                        if (localeAnalysisResult.inferredFromDefault) {
                            addRequestMeta(req, 'localeInferredFromDefault', true);
                        } else {
                            removeRequestMeta(req, 'localeInferredFromDefault');
                        }
                    }
                    let srcPathname = matchedPath;
                    let pageIsDynamic = isDynamicRoute(srcPathname);
                    let paramsResult = {
                        params: false,
                        hasValidParams: false
                    };
                    if (!pageIsDynamic) {
                        const match = await this.matchers.match(srcPathname, {
                            i18n: localeAnalysisResult
                        });
                        // Update the source pathname to the matched page's pathname.
                        if (match) {
                            srcPathname = match.definition.pathname;
                            // The page is dynamic if the params are defined. We know at this
                            // stage that the matched path is not a static page if the params
                            // were parsed from the matched path header.
                            if (typeof match.params !== 'undefined') {
                                pageIsDynamic = true;
                                paramsResult.params = match.params;
                                paramsResult.hasValidParams = true;
                            }
                        }
                    }
                    // The rest of this function can't handle i18n properly, so ensure we
                    // restore the pathname with the locale information stripped from it
                    // now that we're done matching if we're using i18n.
                    if (localeAnalysisResult) {
                        matchedPath = localeAnalysisResult.pathname;
                    }
                    const utils = getServerUtils({
                        pageIsDynamic,
                        page: srcPathname,
                        i18n: this.nextConfig.i18n,
                        basePath: this.nextConfig.basePath,
                        rewrites: ((_this_getRoutesManifest = this.getRoutesManifest()) == null ? void 0 : _this_getRoutesManifest.rewrites) || {
                            beforeFiles: [],
                            afterFiles: [],
                            fallback: []
                        },
                        caseSensitive: !!this.nextConfig.experimental.caseSensitiveRoutes
                    });
                    // Ensure parsedUrl.pathname includes locale before processing
                    // rewrites or they won't match correctly.
                    if (defaultLocale && !pathnameInfo.locale) {
                        parsedUrl.pathname = `/${defaultLocale}${parsedUrl.pathname}`;
                    }
                    // Store a copy of `parsedUrl.query` before calling handleRewrites.
                    // Since `handleRewrites` might add new queries to `parsedUrl.query`.
                    const originQueryParams = {
                        ...parsedUrl.query
                    };
                    const pathnameBeforeRewrite = parsedUrl.pathname;
                    const rewriteParamKeys = Object.keys(utils.handleRewrites(req, parsedUrl));
                    // Create a copy of the query params to avoid mutating the original
                    // object. This prevents any overlapping query params that have the
                    // same normalized key from causing issues.
                    const queryParams = {
                        ...parsedUrl.query
                    };
                    const didRewrite = pathnameBeforeRewrite !== parsedUrl.pathname;
                    if (didRewrite && parsedUrl.pathname) {
                        addRequestMeta(req, 'rewroteURL', parsedUrl.pathname);
                    }
                    const routeParamKeys = new Set();
                    for (const [key, value] of Object.entries(parsedUrl.query)){
                        const normalizedKey = normalizeNextQueryParam(key);
                        if (!normalizedKey) continue;
                        // Remove the prefixed key from the query params because we want
                        // to consume it for the dynamic route matcher.
                        delete parsedUrl.query[key];
                        routeParamKeys.add(normalizedKey);
                        if (typeof value === 'undefined') continue;
                        queryParams[normalizedKey] = Array.isArray(value) ? value.map((v)=>decodeQueryPathParameter(v)) : decodeQueryPathParameter(value);
                    }
                    // interpolate dynamic params and normalize URL if needed
                    if (pageIsDynamic) {
                        let params = {};
                        // If we don't already have valid params, try to parse them from
                        // the query params.
                        if (!paramsResult.hasValidParams) {
                            paramsResult = utils.normalizeDynamicRouteParams(queryParams, false);
                        }
                        // for prerendered ISR paths we attempt parsing the route
                        // params from the URL directly as route-matches may not
                        // contain the correct values due to the filesystem path
                        // matching before the dynamic route has been matched
                        if (!paramsResult.hasValidParams && !isDynamicRoute(normalizedUrlPath)) {
                            let matcherParams = utils.dynamicRouteMatcher == null ? void 0 : utils.dynamicRouteMatcher.call(utils, normalizedUrlPath);
                            if (matcherParams) {
                                utils.normalizeDynamicRouteParams(matcherParams, false);
                                Object.assign(paramsResult.params, matcherParams);
                                paramsResult.hasValidParams = true;
                            }
                        }
                        // if an action request is bypassing a prerender and we
                        // don't have the params in the URL since it was prerendered
                        // and matched during handle: 'filesystem' rather than dynamic route
                        // resolving we need to parse the params from the matched-path.
                        // Note: this is similar to above case but from match-path instead
                        // of from the request URL since a rewrite could cause that to not
                        // match the src pathname
                        if (// we can have a collision with /index and a top-level /[slug]
                        matchedPath !== '/index' && !paramsResult.hasValidParams && !isDynamicRoute(matchedPath)) {
                            let matcherParams = utils.dynamicRouteMatcher == null ? void 0 : utils.dynamicRouteMatcher.call(utils, matchedPath);
                            if (matcherParams) {
                                const curParamsResult = utils.normalizeDynamicRouteParams(matcherParams, false);
                                if (curParamsResult.hasValidParams) {
                                    Object.assign(params, matcherParams);
                                    paramsResult = curParamsResult;
                                }
                            }
                        }
                        if (paramsResult.hasValidParams) {
                            params = paramsResult.params;
                        }
                        const routeMatchesHeader = req.headers['x-now-route-matches'];
                        if (typeof routeMatchesHeader === 'string' && routeMatchesHeader && isDynamicRoute(matchedPath) && !paramsResult.hasValidParams) {
                            const routeMatches = utils.getParamsFromRouteMatches(routeMatchesHeader);
                            if (routeMatches) {
                                paramsResult = utils.normalizeDynamicRouteParams(routeMatches, true);
                                if (paramsResult.hasValidParams) {
                                    params = paramsResult.params;
                                }
                            }
                        }
                        // Try to parse the params from the query if we couldn't parse them
                        // from the route matches but ignore missing optional params.
                        if (!paramsResult.hasValidParams) {
                            paramsResult = utils.normalizeDynamicRouteParams(queryParams, true);
                            if (paramsResult.hasValidParams) {
                                params = paramsResult.params;
                            }
                        }
                        // If the pathname being requested is the same as the source
                        // pathname, and we don't have valid params, we want to use the
                        // default route matches.
                        if (utils.defaultRouteMatches && normalizedUrlPath === srcPathname && !paramsResult.hasValidParams) {
                            params = utils.defaultRouteMatches;
                            // If the route matches header is an empty string, we want to
                            // render a fallback shell. This is because we know this came from
                            // a prerender (it has the header) but it's values were filtered
                            // out (because the allowQuery was empty). If it was undefined
                            // then we know that the request is hitting the lambda directly.
                            if (routeMatchesHeader === '') {
                                addRequestMeta(req, 'renderFallbackShell', true);
                            }
                        }
                        if (params) {
                            matchedPath = utils.interpolateDynamicPath(srcPathname, params);
                            req.url = utils.interpolateDynamicPath(req.url, params);
                            // If the request is for a segment prefetch, we need to update the
                            // segment prefetch request path to include the interpolated
                            // params.
                            let segmentPrefetchRSCRequest = getRequestMeta(req, 'segmentPrefetchRSCRequest');
                            if (segmentPrefetchRSCRequest && isDynamicRoute(segmentPrefetchRSCRequest, false)) {
                                segmentPrefetchRSCRequest = utils.interpolateDynamicPath(segmentPrefetchRSCRequest, params);
                                req.headers[NEXT_ROUTER_SEGMENT_PREFETCH_HEADER.toLowerCase()] = segmentPrefetchRSCRequest;
                                addRequestMeta(req, 'segmentPrefetchRSCRequest', segmentPrefetchRSCRequest);
                            }
                        }
                    }
                    if (pageIsDynamic || didRewrite) {
                        var _utils_defaultRouteRegex;
                        utils.normalizeCdnUrl(req, [
                            ...rewriteParamKeys,
                            ...Object.keys(((_utils_defaultRouteRegex = utils.defaultRouteRegex) == null ? void 0 : _utils_defaultRouteRegex.groups) || {})
                        ]);
                    }
                    // Remove the route `params` keys from `parsedUrl.query` if they are
                    // not in the original query params.
                    // If it's used in both route `params` and query `searchParams`, it should be kept.
                    for (const key of routeParamKeys){
                        if (!(key in originQueryParams)) {
                            delete parsedUrl.query[key];
                        }
                    }
                    parsedUrl.pathname = matchedPath;
                    url.pathname = parsedUrl.pathname;
                    finished = await this.normalizeAndAttachMetadata(req, res, parsedUrl);
                    if (finished) return;
                } catch (err) {
                    if (err instanceof DecodeError || err instanceof NormalizeError) {
                        res.statusCode = 400;
                        return this.renderError(null, req, res, '/_error', {});
                    }
                    throw err;
                }
            }
            addRequestMeta(req, 'isLocaleDomain', Boolean(domainLocale));
            if (pathnameInfo.locale) {
                req.url = formatUrl(url);
                addRequestMeta(req, 'didStripLocale', true);
            }
            // If we aren't in minimal mode or there is no locale in the query
            // string, add the locale to the query string.
            if (!this.minimalMode || !getRequestMeta(req, 'locale')) {
                // If the locale is in the pathname, add it to the query string.
                if (pathnameInfo.locale) {
                    addRequestMeta(req, 'locale', pathnameInfo.locale);
                } else if (defaultLocale) {
                    addRequestMeta(req, 'locale', defaultLocale);
                    addRequestMeta(req, 'localeInferredFromDefault', true);
                }
            }
            // set incremental cache to request meta so it can
            // be passed down for edge functions and the fetch disk
            // cache can be leveraged locally
            if (!this.serverOptions.webServerConfig && !getRequestMeta(req, 'incrementalCache')) {
                const incrementalCache = await this.getIncrementalCache({
                    requestHeaders: Object.assign({}, req.headers)
                });
                incrementalCache.resetRequestCache();
                addRequestMeta(req, 'incrementalCache', incrementalCache);
                globalThis.__incrementalCache = incrementalCache;
            }
            const cacheHandlers = getCacheHandlers();
            if (cacheHandlers) {
                await Promise.all([
                    ...cacheHandlers
                ].map(async (cacheHandler)=>{
                    if ('refreshTags' in cacheHandler) {
                    // Note: cacheHandler.refreshTags() is called lazily before the
                    // first cache entry is retrieved. It allows us to skip the
                    // refresh request if no caches are read at all.
                    } else {
                        const previouslyRevalidatedTags = getPreviouslyRevalidatedTags(req.headers, this.getPrerenderManifest().preview.previewModeId);
                        await cacheHandler.receiveExpiredTags(...previouslyRevalidatedTags);
                    }
                }));
            }
            // set server components HMR cache to request meta so it can be passed
            // down for edge functions
            if (!getRequestMeta(req, 'serverComponentsHmrCache')) {
                addRequestMeta(req, 'serverComponentsHmrCache', this.getServerComponentsHmrCache());
            }
            // when invokePath is specified we can short short circuit resolving
            // we only honor this header if we are inside of a render worker to
            // prevent external users coercing the routing path
            const invokePath = getRequestMeta(req, 'invokePath');
            const useInvokePath = !useMatchedPathHeader && process.env.NEXT_RUNTIME !== 'edge' && invokePath;
            if (useInvokePath) {
                var _this_nextConfig_i18n1;
                const invokeStatus = getRequestMeta(req, 'invokeStatus');
                if (invokeStatus) {
                    const invokeQuery = getRequestMeta(req, 'invokeQuery');
                    if (invokeQuery) {
                        Object.assign(parsedUrl.query, invokeQuery);
                    }
                    res.statusCode = invokeStatus;
                    let err = getRequestMeta(req, 'invokeError') || null;
                    return this.renderError(err, req, res, '/_error', parsedUrl.query);
                }
                const parsedMatchedPath = new URL(invokePath || '/', 'http://n');
                const invokePathnameInfo = getNextPathnameInfo(parsedMatchedPath.pathname, {
                    nextConfig: this.nextConfig,
                    parseData: false
                });
                if (invokePathnameInfo.locale) {
                    addRequestMeta(req, 'locale', invokePathnameInfo.locale);
                }
                if (parsedUrl.pathname !== parsedMatchedPath.pathname) {
                    parsedUrl.pathname = parsedMatchedPath.pathname;
                    addRequestMeta(req, 'rewroteURL', invokePathnameInfo.pathname);
                }
                const normalizeResult = normalizeLocalePath(removePathPrefix(parsedUrl.pathname, this.nextConfig.basePath || ''), (_this_nextConfig_i18n1 = this.nextConfig.i18n) == null ? void 0 : _this_nextConfig_i18n1.locales);
                if (normalizeResult.detectedLocale) {
                    addRequestMeta(req, 'locale', normalizeResult.detectedLocale);
                }
                parsedUrl.pathname = normalizeResult.pathname;
                for (const key of Object.keys(parsedUrl.query)){
                    delete parsedUrl.query[key];
                }
                const invokeQuery = getRequestMeta(req, 'invokeQuery');
                if (invokeQuery) {
                    Object.assign(parsedUrl.query, invokeQuery);
                }
                finished = await this.normalizeAndAttachMetadata(req, res, parsedUrl);
                if (finished) return;
                await this.handleCatchallRenderRequest(req, res, parsedUrl);
                return;
            }
            if (process.env.NEXT_RUNTIME !== 'edge' && getRequestMeta(req, 'middlewareInvoke')) {
                finished = await this.normalizeAndAttachMetadata(req, res, parsedUrl);
                if (finished) return;
                finished = await this.handleCatchallMiddlewareRequest(req, res, parsedUrl);
                if (finished) return;
                const err = new Error();
                err.result = {
                    response: new Response(null, {
                        headers: {
                            'x-middleware-next': '1'
                        }
                    })
                };
                err.bubble = true;
                throw err;
            }
            // This wasn't a request via the matched path or the invoke path, so
            // prepare for a legacy run by removing the base path.
            // ensure we strip the basePath when not using an invoke header
            if (!useMatchedPathHeader && pathnameInfo.basePath) {
                parsedUrl.pathname = removePathPrefix(parsedUrl.pathname, pathnameInfo.basePath);
            }
            res.statusCode = 200;
            return await this.run(req, res, parsedUrl);
        } catch (err) {
            if (err instanceof NoFallbackError) {
                throw err;
            }
            if (err && typeof err === 'object' && err.code === 'ERR_INVALID_URL' || err instanceof DecodeError || err instanceof NormalizeError) {
                res.statusCode = 400;
                return this.renderError(null, req, res, '/_error', {});
            }
            if (this.minimalMode || this.renderOpts.dev || isBubbledError(err) && err.bubble) {
                throw err;
            }
            this.logError(getProperError(err));
            res.statusCode = 500;
            res.body('Internal Server Error').send();
        }
    }
    /**
   * @internal - this method is internal to Next.js and should not be used directly by end-users
   */ getRequestHandlerWithMetadata(meta) {
        const handler = this.getRequestHandler();
        return (req, res, parsedUrl)=>{
            setRequestMeta(req, meta);
            return handler(req, res, parsedUrl);
        };
    }
    getRequestHandler() {
        return this.handleRequest.bind(this);
    }
    setAssetPrefix(prefix) {
        this.renderOpts.assetPrefix = prefix ? prefix.replace(/\/$/, '') : '';
    }
    /**
   * Runs async initialization of server.
   * It is idempotent, won't fire underlying initialization more than once.
   */ async prepare() {
        if (this.prepared) return;
        // Get instrumentation module
        if (!this.instrumentation) {
            this.instrumentation = await this.loadInstrumentationModule();
        }
        if (this.preparedPromise === null) {
            this.preparedPromise = this.prepareImpl().then(()=>{
                this.prepared = true;
                this.preparedPromise = null;
            });
        }
        return this.preparedPromise;
    }
    async prepareImpl() {}
    async loadInstrumentationModule() {}
    async close() {}
    getAppPathRoutes() {
        const appPathRoutes = {};
        Object.keys(this.appPathsManifest || {}).forEach((entry)=>{
            const normalizedPath = normalizeAppPath(entry);
            if (!appPathRoutes[normalizedPath]) {
                appPathRoutes[normalizedPath] = [];
            }
            appPathRoutes[normalizedPath].push(entry);
        });
        return appPathRoutes;
    }
    async run(req, res, parsedUrl) {
        return getTracer().trace(BaseServerSpan.run, async ()=>this.runImpl(req, res, parsedUrl));
    }
    async runImpl(req, res, parsedUrl) {
        await this.handleCatchallRenderRequest(req, res, parsedUrl);
    }
    async pipe(fn, partialContext) {
        return getTracer().trace(BaseServerSpan.pipe, async ()=>this.pipeImpl(fn, partialContext));
    }
    async pipeImpl(fn, partialContext) {
        const ua = partialContext.req.headers['user-agent'] || '';
        const ctx = {
            ...partialContext,
            renderOpts: {
                ...this.renderOpts,
                // `renderOpts.botType` is accumulated in `this.renderImpl()`
                supportsDynamicResponse: !this.renderOpts.botType,
                serveStreamingMetadata: shouldServeStreamingMetadata(ua, this.nextConfig.htmlLimitedBots)
            }
        };
        const payload = await fn(ctx);
        if (payload === null) {
            return;
        }
        const { req, res } = ctx;
        const originalStatus = res.statusCode;
        const { body, type } = payload;
        let { cacheControl } = payload;
        if (!res.sent) {
            const { generateEtags, poweredByHeader, dev } = this.renderOpts;
            // In dev, we should not cache pages for any reason.
            if (dev) {
                res.setHeader('Cache-Control', 'no-store, must-revalidate');
                cacheControl = undefined;
            }
            if (cacheControl && cacheControl.expire === undefined) {
                cacheControl.expire = this.nextConfig.expireTime;
            }
            await this.sendRenderResult(req, res, {
                result: body,
                type,
                generateEtags,
                poweredByHeader,
                cacheControl
            });
            res.statusCode = originalStatus;
        }
    }
    async getStaticHTML(fn, partialContext) {
        const ctx = {
            ...partialContext,
            renderOpts: {
                ...this.renderOpts,
                supportsDynamicResponse: false
            }
        };
        const payload = await fn(ctx);
        if (payload === null) {
            return null;
        }
        return payload.body.toUnchunkedString();
    }
    async render(req, res, pathname, query = {}, parsedUrl, internalRender = false) {
        return getTracer().trace(BaseServerSpan.render, async ()=>this.renderImpl(req, res, pathname, query, parsedUrl, internalRender));
    }
    getWaitUntil() {
        const builtinRequestContext = getBuiltinRequestContext();
        if (builtinRequestContext) {
            // the platform provided a request context.
            // use the `waitUntil` from there, whether actually present or not --
            // if not present, `after` will error.
            // NOTE: if we're in an edge runtime sandbox, this context will be used to forward the outer waitUntil.
            return builtinRequestContext.waitUntil;
        }
        if (this.minimalMode) {
            // we're built for a serverless environment, and `waitUntil` is not available,
            // but using a noop would likely lead to incorrect behavior,
            // because we have no way of keeping the invocation alive.
            // return nothing, and `after` will error if used.
            //
            // NOTE: for edge functions, `NextWebServer` always runs in minimal mode.
            //
            // NOTE: if we're in an edge runtime sandbox, waitUntil will be passed in using "@next/request-context",
            // so we won't get here.
            return undefined;
        }
        return this.getInternalWaitUntil();
    }
    getInternalWaitUntil() {
        return undefined;
    }
    async renderImpl(req, res, pathname, query = {}, parsedUrl, internalRender = false) {
        var _req_url;
        if (!pathname.startsWith('/')) {
            console.warn(`Cannot render page with path "${pathname}", did you mean "/${pathname}"?. See more info here: https://nextjs.org/docs/messages/render-no-starting-slash`);
        }
        if (this.serverOptions.customServer && pathname === '/index' && !await this.hasPage('/index')) {
            // maintain backwards compatibility for custom server
            // (see custom-server integration tests)
            pathname = '/';
        }
        const ua = req.headers['user-agent'] || '';
        this.renderOpts.botType = getBotType(ua);
        // we allow custom servers to call render for all URLs
        // so check if we need to serve a static _next file or not.
        // we don't modify the URL for _next/data request but still
        // call render so we special case this to prevent an infinite loop
        if (!internalRender && !this.minimalMode && !getRequestMeta(req, 'isNextDataReq') && (((_req_url = req.url) == null ? void 0 : _req_url.match(/^\/_next\//)) || this.hasStaticDir && req.url.match(/^\/static\//))) {
            return this.handleRequest(req, res, parsedUrl);
        }
        if (isBlockedPage(pathname)) {
            return this.render404(req, res, parsedUrl);
        }
        return this.pipe((ctx)=>this.renderToResponse(ctx), {
            req,
            res,
            pathname,
            query
        });
    }
    async getStaticPaths({ pathname }) {
        var _this_getPrerenderManifest_dynamicRoutes_pathname;
        // Read whether or not fallback should exist from the manifest.
        const fallbackField = (_this_getPrerenderManifest_dynamicRoutes_pathname = this.getPrerenderManifest().dynamicRoutes[pathname]) == null ? void 0 : _this_getPrerenderManifest_dynamicRoutes_pathname.fallback;
        return {
            // `staticPaths` is intentionally set to `undefined` as it should've
            // been caught when checking disk data.
            staticPaths: undefined,
            fallbackMode: parseFallbackField(fallbackField)
        };
    }
    async renderToResponseWithComponents(requestContext, findComponentsResult) {
        return getTracer().trace(BaseServerSpan.renderToResponseWithComponents, async ()=>this.renderToResponseWithComponentsImpl(requestContext, findComponentsResult));
    }
    pathCouldBeIntercepted(resolvedPathname) {
        return isInterceptionRouteAppPath(resolvedPathname) || this.interceptionRoutePatterns.some((regexp)=>{
            return regexp.test(resolvedPathname);
        });
    }
    setVaryHeader(req, res, isAppPath, resolvedPathname) {
        const baseVaryHeader = `${RSC_HEADER}, ${NEXT_ROUTER_STATE_TREE_HEADER}, ${NEXT_ROUTER_PREFETCH_HEADER}, ${NEXT_ROUTER_SEGMENT_PREFETCH_HEADER}`;
        const isRSCRequest = getRequestMeta(req, 'isRSCRequest') ?? false;
        let addedNextUrlToVary = false;
        if (isAppPath && this.pathCouldBeIntercepted(resolvedPathname)) {
            // Interception route responses can vary based on the `Next-URL` header.
            // We use the Vary header to signal this behavior to the client to properly cache the response.
            res.appendHeader('vary', `${baseVaryHeader}, ${NEXT_URL}`);
            addedNextUrlToVary = true;
        } else if (isAppPath || isRSCRequest) {
            // We don't need to include `Next-URL` in the Vary header for non-interception routes since it won't affect the response.
            // We also set this header for pages to avoid caching issues when navigating between pages and app.
            res.appendHeader('vary', baseVaryHeader);
        }
        if (!addedNextUrlToVary) {
            // Remove `Next-URL` from the request headers we determined it wasn't necessary to include in the Vary header.
            // This is to avoid any dependency on the `Next-URL` header being present when preparing the response.
            delete req.headers[NEXT_URL];
        }
    }
    async renderToResponseWithComponentsImpl({ req, res, pathname, renderOpts: opts }, { components, query }) {
        var _components_Component, _this_nextConfig_i18n, _this, _this_nextConfig_i18n1, _cacheEntry_value;
        if (pathname === UNDERSCORE_NOT_FOUND_ROUTE) {
            pathname = '/404';
        }
        const isErrorPathname = pathname === '/_error';
        const is404Page = pathname === '/404' || isErrorPathname && res.statusCode === 404;
        const is500Page = pathname === '/500' || isErrorPathname && res.statusCode === 500;
        const isAppPath = components.isAppPath === true;
        const hasServerProps = !!components.getServerSideProps;
        let hasGetStaticPaths = !!components.getStaticPaths;
        const isPossibleServerAction = getIsPossibleServerAction(req);
        const hasGetInitialProps = !!((_components_Component = components.Component) == null ? void 0 : _components_Component.getInitialProps);
        let isSSG = !!components.getStaticProps;
        // NOTE: Don't delete headers[RSC] yet, it still needs to be used in renderToHTML later
        const isRSCRequest = getRequestMeta(req, 'isRSCRequest') ?? false;
        // Not all CDNs respect the Vary header when caching. We must assume that
        // only the URL is used to vary the responses. The Next client computes a
        // hash of the header values and sends it as a search param. Before
        // responding to a request, we must verify that the hash matches the
        // expected value. Neglecting to do this properly can lead to cache
        // poisoning attacks on certain CDNs.
        if (!this.minimalMode && this.nextConfig.experimental.validateRSCRequestHeaders && isRSCRequest) {
            const headers = req.headers;
            const expectedHash = computeCacheBustingSearchParam(headers[NEXT_ROUTER_PREFETCH_HEADER.toLowerCase()], headers[NEXT_ROUTER_SEGMENT_PREFETCH_HEADER.toLowerCase()], headers[NEXT_ROUTER_STATE_TREE_HEADER.toLowerCase()], headers[NEXT_URL.toLowerCase()]);
            const actualHash = getRequestMeta(req, 'cacheBustingSearchParam') ?? new URL(req.url || '', 'http://localhost').searchParams.get(NEXT_RSC_UNION_QUERY);
            if (expectedHash !== actualHash) {
                // The hash sent by the client does not match the expected value.
                // Redirect to the URL with the correct cache-busting search param.
                // This prevents cache poisoning attacks on CDNs that don't respect Vary headers.
                // Note: When no headers are present, expectedHash is empty string and client
                // must send `_rsc` param, otherwise actualHash is null and hash check fails.
                const url = new URL(req.url || '', 'http://localhost');
                setCacheBustingSearchParamWithHash(url, expectedHash);
                res.statusCode = 307;
                res.setHeader('location', `${url.pathname}${url.search}`);
                res.body('').send();
                return null;
            }
        }
        // Compute the iSSG cache key. We use the rewroteUrl since
        // pages with fallback: false are allowed to be rewritten to
        // and we need to look up the path by the rewritten path
        let urlPathname = parseUrl(req.url || '').pathname || '/';
        let resolvedUrlPathname = getRequestMeta(req, 'rewroteURL') || urlPathname;
        this.setVaryHeader(req, res, isAppPath, resolvedUrlPathname);
        let staticPaths;
        let fallbackMode;
        let hasFallback = false;
        const isDynamic = isDynamicRoute(components.page);
        const prerenderManifest = this.getPrerenderManifest();
        if (isAppPath && isDynamic) {
            const pathsResult = await this.getStaticPaths({
                pathname,
                page: components.page,
                isAppPath,
                requestHeaders: req.headers
            });
            staticPaths = pathsResult.staticPaths;
            fallbackMode = pathsResult.fallbackMode;
            hasFallback = typeof fallbackMode !== 'undefined';
            if (this.nextConfig.output === 'export') {
                const page = components.page;
                if (!staticPaths) {
                    throw Object.defineProperty(new Error(`Page "${page}" is missing exported function "generateStaticParams()", which is required with "output: export" config.`), "__NEXT_ERROR_CODE", {
                        value: "E353",
                        enumerable: false,
                        configurable: true
                    });
                }
                const resolvedWithoutSlash = removeTrailingSlash(resolvedUrlPathname);
                if (!staticPaths.includes(resolvedWithoutSlash)) {
                    throw Object.defineProperty(new Error(`Page "${page}" is missing param "${resolvedWithoutSlash}" in "generateStaticParams()", which is required with "output: export" config.`), "__NEXT_ERROR_CODE", {
                        value: "E443",
                        enumerable: false,
                        configurable: true
                    });
                }
            }
            if (hasFallback) {
                hasGetStaticPaths = true;
            }
        }
        if (hasFallback || (staticPaths == null ? void 0 : staticPaths.includes(resolvedUrlPathname)) || // this signals revalidation in deploy environments
        // TODO: make this more generic
        req.headers['x-now-route-matches']) {
            isSSG = true;
        } else if (!this.renderOpts.dev) {
            isSSG ||= !!prerenderManifest.routes[toRoute(pathname)];
        }
        // Toggle whether or not this is a Data request
        const isNextDataRequest = !!(getRequestMeta(req, 'isNextDataReq') || req.headers['x-nextjs-data'] && this.serverOptions.webServerConfig) && (isSSG || hasServerProps);
        /**
     * If true, this indicates that the request being made is for an app
     * prefetch request.
     */ const isPrefetchRSCRequest = getRequestMeta(req, 'isPrefetchRSCRequest') ?? false;
        // when we are handling a middleware prefetch and it doesn't
        // resolve to a static data route we bail early to avoid
        // unexpected SSR invocations
        if (!isSSG && req.headers['x-middleware-prefetch'] && !(is404Page || pathname === '/_error')) {
            res.setHeader(MATCHED_PATH_HEADER, pathname);
            res.setHeader('x-middleware-skip', '1');
            res.setHeader('cache-control', 'private, no-cache, no-store, max-age=0, must-revalidate');
            res.body('{}').send();
            return null;
        }
        // normalize req.url for SSG paths as it is not exposed
        // to getStaticProps and the asPath should not expose /_next/data
        if (isSSG && this.minimalMode && req.headers[MATCHED_PATH_HEADER] && req.url.startsWith('/_next/data')) {
            req.url = this.stripNextDataPath(req.url);
        }
        const locale = getRequestMeta(req, 'locale');
        const defaultLocale = isSSG ? (_this_nextConfig_i18n = this.nextConfig.i18n) == null ? void 0 : _this_nextConfig_i18n.defaultLocale : getRequestMeta(req, 'defaultLocale');
        if (!!req.headers['x-nextjs-data'] && (!res.statusCode || res.statusCode === 200)) {
            res.setHeader('x-nextjs-matched-path', `${locale ? `/${locale}` : ''}${pathname}`);
        }
        let routeModule;
        if (components.routeModule) {
            routeModule = components.routeModule;
        }
        /**
     * If the route being rendered is an app page, and the ppr feature has been
     * enabled, then the given route _could_ support PPR.
     */ const couldSupportPPR = this.isAppPPREnabled && typeof routeModule !== 'undefined' && isAppPageRouteModule(routeModule);
        // When enabled, this will allow the use of the `?__nextppronly` query to
        // enable debugging of the static shell.
        const hasDebugStaticShellQuery = process.env.__NEXT_EXPERIMENTAL_STATIC_SHELL_DEBUGGING === '1' && typeof query.__nextppronly !== 'undefined' && couldSupportPPR;
        // When enabled, this will allow the use of the `?__nextppronly` query
        // to enable debugging of the fallback shell.
        const hasDebugFallbackShellQuery = hasDebugStaticShellQuery && query.__nextppronly === 'fallback';
        // This page supports PPR if it is marked as being `PARTIALLY_STATIC` in the
        // prerender manifest and this is an app page.
        const isRoutePPREnabled = couldSupportPPR && (((_this = prerenderManifest.routes[pathname] ?? prerenderManifest.dynamicRoutes[pathname]) == null ? void 0 : _this.renderingMode) === 'PARTIALLY_STATIC' || // Ideally we'd want to check the appConfig to see if this page has PPR
        // enabled or not, but that would require plumbing the appConfig through
        // to the server during development. We assume that the page supports it
        // but only during development.
        hasDebugStaticShellQuery && (this.renderOpts.dev === true || this.experimentalTestProxy === true));
        const isDebugStaticShell = hasDebugStaticShellQuery && isRoutePPREnabled;
        // We should enable debugging dynamic accesses when the static shell
        // debugging has been enabled and we're also in development mode.
        const isDebugDynamicAccesses = isDebugStaticShell && this.renderOpts.dev === true;
        const isDebugFallbackShell = hasDebugFallbackShellQuery && isRoutePPREnabled;
        // If we're in minimal mode, then try to get the postponed information from
        // the request metadata. If available, use it for resuming the postponed
        // render.
        const minimalPostponed = isRoutePPREnabled ? getRequestMeta(req, 'postponed') : undefined;
        // If PPR is enabled, and this is a RSC request (but not a prefetch), then
        // we can use this fact to only generate the flight data for the request
        // because we can't cache the HTML (as it's also dynamic).
        const isDynamicRSCRequest = isRoutePPREnabled && isRSCRequest && !isPrefetchRSCRequest;
        // Need to read this before it's stripped by stripFlightHeaders. We don't
        // need to transfer it to the request meta because it's only read
        // within this function; the static segment data should have already been
        // generated, so we will always either return a static response or a 404.
        const segmentPrefetchHeader = getRequestMeta(req, 'segmentPrefetchRSCRequest');
        const isHtmlBot = isHtmlBotRequest(req);
        if (isHtmlBot && isRoutePPREnabled) {
            isSSG = false;
            this.renderOpts.serveStreamingMetadata = false;
        }
        // we need to ensure the status code if /404 is visited directly
        if (is404Page && !isNextDataRequest && !isRSCRequest) {
            res.statusCode = 404;
        }
        // ensure correct status is set when visiting a status page
        // directly e.g. /500
        if (STATIC_STATUS_PAGES.includes(pathname)) {
            res.statusCode = parseInt(pathname.slice(1), 10);
        }
        if (// Server actions can use non-GET/HEAD methods.
        !isPossibleServerAction && // Resume can use non-GET/HEAD methods.
        !minimalPostponed && !is404Page && !is500Page && pathname !== '/_error' && req.method !== 'HEAD' && req.method !== 'GET' && (typeof components.Component === 'string' || isSSG)) {
            res.statusCode = 405;
            res.setHeader('Allow', [
                'GET',
                'HEAD'
            ]);
            res.body('Method Not Allowed').send();
            return null;
        }
        // handle static page
        if (typeof components.Component === 'string') {
            return {
                type: 'html',
                // TODO: Static pages should be serialized as RenderResult
                body: RenderResult.fromStatic(components.Component)
            };
        }
        // Ensure that if the `amp` query parameter is falsy that we remove it from
        // the query object. This ensures it won't be found by the `in` operator.
        if ('amp' in query && !query.amp) delete query.amp;
        if (opts.supportsDynamicResponse === true) {
            var _components_Document;
            const ua = req.headers['user-agent'] || '';
            const isBotRequest = isBot(ua);
            const isSupportedDocument = typeof ((_components_Document = components.Document) == null ? void 0 : _components_Document.getInitialProps) !== 'function' || // The built-in `Document` component also supports dynamic HTML for concurrent mode.
            NEXT_BUILTIN_DOCUMENT in components.Document;
            // Disable dynamic HTML in cases that we know it won't be generated,
            // so that we can continue generating a cache key when possible.
            // TODO-APP: should the first render for a dynamic app path
            // be static so we can collect revalidate and populate the
            // cache if there are no dynamic data requirements
            opts.supportsDynamicResponse = !isSSG && !isBotRequest && !query.amp && isSupportedDocument;
        }
        // In development, we always want to generate dynamic HTML.
        if (!isNextDataRequest && isAppPath && opts.dev) {
            opts.supportsDynamicResponse = true;
        }
        const locales = (_this_nextConfig_i18n1 = this.nextConfig.i18n) == null ? void 0 : _this_nextConfig_i18n1.locales;
        let previewData;
        let isPreviewMode = false;
        if (hasServerProps || isSSG || isAppPath) {
            // For the edge runtime, we don't support preview mode in SSG.
            if (process.env.NEXT_RUNTIME !== 'edge') {
                const { tryGetPreviewData } = require('./api-utils/node/try-get-preview-data');
                previewData = tryGetPreviewData(req, res, this.renderOpts.previewProps, !!this.nextConfig.experimental.multiZoneDraftMode);
                isPreviewMode = previewData !== false;
            }
        }
        // If this is a request for an app path that should be statically generated
        // and we aren't in the edge runtime, strip the flight headers so it will
        // generate the static response.
        if (isAppPath && !opts.dev && !isPreviewMode && isSSG && isRSCRequest && !isDynamicRSCRequest && (!isEdgeRuntime(opts.runtime) || this.serverOptions.webServerConfig)) {
            stripFlightHeaders(req.headers);
        }
        let { isOnDemandRevalidate, revalidateOnlyGenerated } = checkIsOnDemandRevalidate(req, this.renderOpts.previewProps);
        if (isSSG && this.minimalMode && req.headers[MATCHED_PATH_HEADER]) {
            // the url value is already correct when the matched-path header is set
            resolvedUrlPathname = urlPathname;
        }
        urlPathname = removeTrailingSlash(urlPathname);
        resolvedUrlPathname = removeTrailingSlash(resolvedUrlPathname);
        if (this.localeNormalizer) {
            resolvedUrlPathname = this.localeNormalizer.normalize(resolvedUrlPathname);
        }
        const handleRedirect = (pageData)=>{
            const redirect = {
                destination: pageData.pageProps.__N_REDIRECT,
                statusCode: pageData.pageProps.__N_REDIRECT_STATUS,
                basePath: pageData.pageProps.__N_REDIRECT_BASE_PATH
            };
            const statusCode = getRedirectStatus(redirect);
            const { basePath } = this.nextConfig;
            if (basePath && redirect.basePath !== false && redirect.destination.startsWith('/')) {
                redirect.destination = `${basePath}${redirect.destination}`;
            }
            if (redirect.destination.startsWith('/')) {
                redirect.destination = normalizeRepeatedSlashes(redirect.destination);
            }
            res.redirect(redirect.destination, statusCode).body(redirect.destination).send();
        };
        // remove /_next/data prefix from urlPathname so it matches
        // for direct page visit and /_next/data visit
        if (isNextDataRequest) {
            resolvedUrlPathname = this.stripNextDataPath(resolvedUrlPathname);
            urlPathname = this.stripNextDataPath(urlPathname);
        }
        let ssgCacheKey = null;
        if (!isPreviewMode && isSSG && !opts.supportsDynamicResponse && !isPossibleServerAction && !minimalPostponed && !isDynamicRSCRequest) {
            ssgCacheKey = `${locale ? `/${locale}` : ''}${(pathname === '/' || resolvedUrlPathname === '/') && locale ? '' : resolvedUrlPathname}${query.amp ? '.amp' : ''}`;
        }
        if ((is404Page || is500Page) && isSSG) {
            ssgCacheKey = `${locale ? `/${locale}` : ''}${pathname}${query.amp ? '.amp' : ''}`;
        }
        if (ssgCacheKey) {
            ssgCacheKey = decodePathParams(ssgCacheKey);
            // ensure /index and / is normalized to one key
            ssgCacheKey = ssgCacheKey === '/index' && pathname === '/' ? '/' : ssgCacheKey;
        }
        // use existing incrementalCache instance if available
        const incrementalCache = process.env.NEXT_RUNTIME === 'edge' && globalThis.__incrementalCache ? globalThis.__incrementalCache : await this.getIncrementalCache({
            requestHeaders: Object.assign({}, req.headers)
        });
        // TODO: investigate, this is not safe across multiple concurrent requests
        incrementalCache.resetRequestCache();
        const doRender = async ({ postponed, pagesFallback = false, fallbackRouteParams })=>{
            // In development, we always want to generate dynamic HTML.
            let supportsDynamicResponse = // If we're in development, we always support dynamic HTML, unless it's
            // a data request, in which case we only produce static HTML.
            !isNextDataRequest && opts.dev === true || // If this is not SSG or does not have static paths, then it supports
            // dynamic HTML.
            !isSSG && !hasGetStaticPaths || // If this request has provided postponed data, it supports dynamic
            // HTML.
            typeof postponed === 'string' || // If this is a dynamic RSC request, then this render supports dynamic
            // HTML (it's dynamic).
            isDynamicRSCRequest;
            const origQuery = parseUrl(req.url || '', true).query;
            // clear any dynamic route params so they aren't in
            // the resolvedUrl
            if (opts.params) {
                Object.keys(opts.params).forEach((key)=>{
                    delete origQuery[key];
                });
            }
            const hadTrailingSlash = urlPathname !== '/' && this.nextConfig.trailingSlash;
            const resolvedUrl = formatUrl({
                pathname: `${resolvedUrlPathname}${hadTrailingSlash ? '/' : ''}`,
                // make sure to only add query values from original URL
                query: origQuery
            });
            // When html bots request PPR page, perform the full dynamic rendering.
            const shouldWaitOnAllReady = isHtmlBot && isRoutePPREnabled;
            const renderOpts = {
                ...components,
                ...opts,
                ...isAppPath ? {
                    incrementalCache,
                    // This is a revalidation request if the request is for a static
                    // page and it is not being resumed from a postponed render and
                    // it is not a dynamic RSC request then it is a revalidation
                    // request.
                    isRevalidate: isSSG && !postponed && !isDynamicRSCRequest,
                    serverActions: this.nextConfig.experimental.serverActions
                } : {},
                isNextDataRequest,
                resolvedUrl,
                locale,
                locales,
                defaultLocale,
                multiZoneDraftMode: this.nextConfig.experimental.multiZoneDraftMode,
                // For getServerSideProps and getInitialProps we need to ensure we use the original URL
                // and not the resolved URL to prevent a hydration mismatch on
                // asPath
                resolvedAsPath: hasServerProps || hasGetInitialProps ? formatUrl({
                    // we use the original URL pathname less the _next/data prefix if
                    // present
                    pathname: `${urlPathname}${hadTrailingSlash ? '/' : ''}`,
                    query: origQuery
                }) : resolvedUrl,
                experimental: {
                    ...opts.experimental,
                    isRoutePPREnabled
                },
                supportsDynamicResponse,
                shouldWaitOnAllReady,
                isOnDemandRevalidate,
                isDraftMode: isPreviewMode,
                isPossibleServerAction,
                postponed,
                waitUntil: this.getWaitUntil(),
                onClose: res.onClose.bind(res),
                onAfterTaskError: undefined,
                // only available in dev
                setIsrStatus: this.setIsrStatus
            };
            if (isDebugStaticShell || isDebugDynamicAccesses) {
                supportsDynamicResponse = false;
                renderOpts.nextExport = true;
                renderOpts.supportsDynamicResponse = false;
                renderOpts.isStaticGeneration = true;
                renderOpts.isRevalidate = true;
                renderOpts.isDebugDynamicAccesses = isDebugDynamicAccesses;
            }
            // Legacy render methods will return a render result that needs to be
            // served by the server.
            let result;
            if (routeModule) {
                if (isAppRouteRouteModule(routeModule) || isPagesRouteModule(routeModule) || isAppPageRouteModule(routeModule)) {
                    // An OPTIONS request to a page handler is invalid.
                    if (req.method === 'OPTIONS' && !is404Page && !isAppRouteRouteModule(routeModule)) {
                        await sendResponse(req, res, new Response(null, {
                            status: 400
                        }));
                        return null;
                    }
                    const request = isNodeNextRequest(req) ? req.originalRequest : req;
                    const response = isNodeNextResponse(res) ? res.originalResponse : res;
                    if (components.ComponentMod.handler && process.env.NEXT_RUNTIME !== 'edge') {
                        const parsedInitUrl = parseUrl(getRequestMeta(req, 'initURL') || req.url);
                        let initPathname = parsedInitUrl.pathname || '/';
                        for (const normalizer of [
                            this.normalizers.segmentPrefetchRSC,
                            this.normalizers.prefetchRSC,
                            this.normalizers.rsc
                        ]){
                            if (normalizer == null ? void 0 : normalizer.match(initPathname)) {
                                initPathname = normalizer.normalize(initPathname);
                            }
                        }
                        request.url = `${initPathname}${parsedInitUrl.search || ''}`;
                        // propagate the request context for dev
                        setRequestMeta(request, getRequestMeta(req));
                        addRequestMeta(request, 'projectDir', this.dir);
                        addRequestMeta(request, 'distDir', this.distDir);
                        addRequestMeta(request, 'isIsrFallback', pagesFallback);
                        addRequestMeta(request, 'query', query);
                        addRequestMeta(request, 'params', opts.params);
                        addRequestMeta(request, 'ampValidator', this.renderOpts.ampValidator);
                        addRequestMeta(request, 'minimalMode', this.minimalMode);
                        if (renderOpts.err) {
                            addRequestMeta(request, 'invokeError', renderOpts.err);
                        }
                        const handler = components.ComponentMod.handler;
                        const maybeDevRequest = // we need to capture fetch metrics when they are set
                        // and can't wait for handler to resolve as the fetch
                        // metrics are logged on response close which happens
                        // before handler resolves
                        process.env.NODE_ENV === 'development' ? new Proxy(request, {
                            get (target, prop) {
                                if (typeof target[prop] === 'function') {
                                    return target[prop].bind(target);
                                }
                                return target[prop];
                            },
                            set (target, prop, value) {
                                if (prop === 'fetchMetrics') {
                                    ;
                                    req.fetchMetrics = value;
                                }
                                target[prop] = value;
                                return true;
                            }
                        }) : request;
                        result = await handler(maybeDevRequest, response, {
                            waitUntil: this.getWaitUntil()
                        });
                        // response is handled fully in handler
                        return null;
                    } else {
                        if (isPagesRouteModule(routeModule)) {
                            // Due to the way we pass data by mutating `renderOpts`, we can't extend
                            // the object here but only updating its `clientReferenceManifest` and
                            // `nextFontManifest` properties.
                            // https://github.com/vercel/next.js/blob/df7cbd904c3bd85f399d1ce90680c0ecf92d2752/packages/next/server/render.tsx#L947-L952
                            renderOpts.nextFontManifest = this.nextFontManifest;
                            renderOpts.clientReferenceManifest = components.clientReferenceManifest;
                            // Call the built-in render method on the module.
                            try {
                                result = await routeModule.render(request, response, {
                                    page: pathname,
                                    params: opts.params,
                                    query,
                                    renderOpts,
                                    sharedContext: {
                                        buildId: this.buildId,
                                        deploymentId: this.nextConfig.deploymentId,
                                        customServer: this.serverOptions.customServer || undefined
                                    },
                                    renderContext: {
                                        isFallback: pagesFallback,
                                        isDraftMode: renderOpts.isDraftMode,
                                        developmentNotFoundSourcePage: getRequestMeta(req, 'developmentNotFoundSourcePage')
                                    }
                                });
                            } catch (err) {
                                await this.instrumentationOnRequestError(err, req, {
                                    routerKind: 'Pages Router',
                                    routePath: pathname,
                                    routeType: 'render',
                                    revalidateReason: getRevalidateReason({
                                        isRevalidate: isSSG,
                                        isOnDemandRevalidate: renderOpts.isOnDemandRevalidate
                                    })
                                });
                                throw err;
                            }
                        } else {
                            const module = components.routeModule;
                            // Due to the way we pass data by mutating `renderOpts`, we can't extend the
                            // object here but only updating its `nextFontManifest` field.
                            // https://github.com/vercel/next.js/blob/df7cbd904c3bd85f399d1ce90680c0ecf92d2752/packages/next/server/render.tsx#L947-L952
                            renderOpts.nextFontManifest = this.nextFontManifest;
                            const context = {
                                page: is404Page ? '/404' : pathname,
                                params: opts.params,
                                query,
                                fallbackRouteParams,
                                renderOpts,
                                serverComponentsHmrCache: this.getServerComponentsHmrCache(),
                                sharedContext: {
                                    buildId: this.buildId
                                }
                            };
                            // TODO: adapt for putting the RDC inside the postponed data
                            // If we're in dev, and this isn't a prefetch or a server action,
                            // we should seed the resume data cache.
                            if (this.nextConfig.experimental.dynamicIO && this.renderOpts.dev && !isPrefetchRSCRequest && !isPossibleServerAction) {
                                const warmup = await module.warmup(req, res, context);
                                // If the warmup is successful, we should use the resume data
                                // cache from the warmup.
                                if (warmup.metadata.renderResumeDataCache) {
                                    renderOpts.renderResumeDataCache = warmup.metadata.renderResumeDataCache;
                                }
                            }
                            // Call the built-in render method on the module.
                            result = await module.render(req, res, context);
                        }
                    }
                } else {
                    throw Object.defineProperty(new Error('Invariant: Unknown route module type'), "__NEXT_ERROR_CODE", {
                        value: "E450",
                        enumerable: false,
                        configurable: true
                    });
                }
            } else {
                // If we didn't match a page, we should fallback to using the legacy
                // render method.
                result = await this.renderHTML(req, res, pathname, query, renderOpts);
            }
            const { metadata } = result;
            const { cacheControl, headers = {}, // Add any fetch tags that were on the page to the response headers.
            fetchTags: cacheTags } = metadata;
            if (cacheTags) {
                headers[NEXT_CACHE_TAGS_HEADER] = cacheTags;
            }
            // Pull any fetch metrics from the render onto the request.
            ;
            req.fetchMetrics = metadata.fetchMetrics;
            // we don't throw static to dynamic errors in dev as isSSG
            // is a best guess in dev since we don't have the prerender pass
            // to know whether the path is actually static or not
            if (isAppPath && isSSG && (cacheControl == null ? void 0 : cacheControl.revalidate) === 0 && !this.renderOpts.dev && !isRoutePPREnabled) {
                const staticBailoutInfo = metadata.staticBailoutInfo;
                const err = Object.defineProperty(new Error(`Page changed from static to dynamic at runtime ${urlPathname}${(staticBailoutInfo == null ? void 0 : staticBailoutInfo.description) ? `, reason: ${staticBailoutInfo.description}` : ``}` + `\nsee more here https://nextjs.org/docs/messages/app-static-to-dynamic-error`), "__NEXT_ERROR_CODE", {
                    value: "E132",
                    enumerable: false,
                    configurable: true
                });
                if (staticBailoutInfo == null ? void 0 : staticBailoutInfo.stack) {
                    const stack = staticBailoutInfo.stack;
                    err.stack = err.message + stack.substring(stack.indexOf('\n'));
                }
                throw err;
            }
            // Based on the metadata, we can determine what kind of cache result we
            // should return.
            // Handle `isNotFound`.
            if ('isNotFound' in metadata && metadata.isNotFound) {
                return {
                    value: null,
                    cacheControl
                };
            }
            // Handle `isRedirect`.
            if (metadata.isRedirect) {
                return {
                    value: {
                        kind: CachedRouteKind.REDIRECT,
                        props: metadata.pageData ?? metadata.flightData
                    },
                    cacheControl
                };
            }
            // Handle `isNull`.
            if (result.isNull) {
                return null;
            }
            // We now have a valid HTML result that we can return to the user.
            if (isAppPath) {
                return {
                    value: {
                        kind: CachedRouteKind.APP_PAGE,
                        html: result,
                        headers,
                        rscData: metadata.flightData,
                        postponed: metadata.postponed,
                        status: metadata.statusCode,
                        segmentData: metadata.segmentData
                    },
                    cacheControl
                };
            }
            return {
                value: {
                    kind: CachedRouteKind.PAGES,
                    html: result,
                    pageData: metadata.pageData ?? metadata.flightData,
                    headers,
                    status: isAppPath ? res.statusCode : undefined
                },
                cacheControl
            };
        };
        let responseGenerator = async ({ hasResolved, previousCacheEntry, isRevalidating })=>{
            const isProduction = !this.renderOpts.dev;
            const didRespond = hasResolved || res.sent;
            // If we haven't found the static paths for the route, then do it now.
            if (!staticPaths && isDynamic) {
                if (hasGetStaticPaths) {
                    const pathsResult = await this.getStaticPaths({
                        pathname,
                        requestHeaders: req.headers,
                        isAppPath,
                        page: components.page
                    });
                    staticPaths = pathsResult.staticPaths;
                    fallbackMode = pathsResult.fallbackMode;
                } else {
                    staticPaths = undefined;
                    fallbackMode = FallbackMode.NOT_FOUND;
                }
            }
            // When serving a bot request, we want to serve a blocking render and not
            // the prerendered page. This ensures that the correct content is served
            // to the bot in the head.
            if (fallbackMode === FallbackMode.PRERENDER && isBot(req.headers['user-agent'] || '')) {
                fallbackMode = FallbackMode.BLOCKING_STATIC_RENDER;
            }
            // skip on-demand revalidate if cache is not present and
            // revalidate-if-generated is set
            if (isOnDemandRevalidate && revalidateOnlyGenerated && !previousCacheEntry && !this.minimalMode) {
                await this.render404(req, res);
                return null;
            }
            if ((previousCacheEntry == null ? void 0 : previousCacheEntry.isStale) === -1) {
                isOnDemandRevalidate = true;
            }
            // TODO: adapt for PPR
            // only allow on-demand revalidate for fallback: true/blocking
            // or for prerendered fallback: false paths
            if (isOnDemandRevalidate && (fallbackMode !== FallbackMode.NOT_FOUND || previousCacheEntry)) {
                fallbackMode = FallbackMode.BLOCKING_STATIC_RENDER;
            }
            // We use `ssgCacheKey` here as it is normalized to match the encoding
            // from getStaticPaths along with including the locale.
            //
            // We use the `resolvedUrlPathname` for the development case when this
            // is an app path since it doesn't include locale information.
            //
            // We decode the `resolvedUrlPathname` to correctly match the app path
            // with prerendered paths.
            let staticPathKey = ssgCacheKey;
            if (!staticPathKey && opts.dev && isAppPath) {
                staticPathKey = decodePathParams(resolvedUrlPathname);
            }
            if (staticPathKey && query.amp) {
                staticPathKey = staticPathKey.replace(/\.amp$/, '');
            }
            const isPageIncludedInStaticPaths = staticPathKey && (staticPaths == null ? void 0 : staticPaths.includes(staticPathKey));
            // When experimental compile is used, no pages have been prerendered,
            // so they should all be blocking.
            if (this.nextConfig.experimental.isExperimentalCompile) {
                fallbackMode = FallbackMode.BLOCKING_STATIC_RENDER;
            }
            // When we did not respond from cache, we need to choose to block on
            // rendering or return a skeleton.
            //
            // - Data requests always block.
            // - Blocking mode fallback always blocks.
            // - Preview mode toggles all pages to be resolved in a blocking manner.
            // - Non-dynamic pages should block (though this is an impossible
            //   case in production).
            // - Dynamic pages should return their skeleton if not defined in
            //   getStaticPaths, then finish the data request on the client-side.
            //
            if (process.env.NEXT_RUNTIME !== 'edge' && !this.minimalMode && fallbackMode !== FallbackMode.BLOCKING_STATIC_RENDER && staticPathKey && !didRespond && !isPreviewMode && isDynamic && (isProduction || !staticPaths || !isPageIncludedInStaticPaths)) {
                if (// In development, fall through to render to handle missing
                // getStaticPaths.
                (isProduction || staticPaths && (staticPaths == null ? void 0 : staticPaths.length) > 0) && // When fallback isn't present, abort this render so we 404
                fallbackMode === FallbackMode.NOT_FOUND) {
                    throw new NoFallbackError();
                }
                let fallbackResponse;
                // If this is a pages router page.
                if (isPagesRouteModule(components.routeModule) && !isNextDataRequest) {
                    // We use the response cache here to handle the revalidation and
                    // management of the fallback shell.
                    fallbackResponse = await this.responseCache.get(isProduction ? locale ? `/${locale}${pathname}` : pathname : null, // This is the response generator for the fallback shell.
                    async ({ previousCacheEntry: previousFallbackCacheEntry = null })=>{
                        // For the pages router, fallbacks cannot be revalidated or
                        // generated in production. In the case of a missing fallback,
                        // we return null, but if it's being revalidated, we just return
                        // the previous fallback cache entry. This preserves the previous
                        // behavior.
                        if (isProduction) {
                            return toResponseCacheEntry(previousFallbackCacheEntry);
                        }
                        // We pass `undefined` and `null` as it doesn't apply to the pages
                        // router.
                        return doRender({
                            postponed: undefined,
                            // For the pages router, fallbacks can only be generated on
                            // demand in development, so if we're not in production, and we
                            // aren't a app path.
                            pagesFallback: true,
                            fallbackRouteParams: null
                        });
                    }, {
                        routeKind: RouteKind.PAGES,
                        incrementalCache,
                        isRoutePPREnabled,
                        isFallback: true
                    });
                } else if (isRoutePPREnabled && isAppPageRouteModule(components.routeModule) && !isRSCRequest) {
                    // We use the response cache here to handle the revalidation and
                    // management of the fallback shell.
                    fallbackResponse = await this.responseCache.get(isProduction ? pathname : null, // This is the response generator for the fallback shell.
                    async ()=>doRender({
                            // We pass `undefined` as rendering a fallback isn't resumed
                            // here.
                            postponed: undefined,
                            pagesFallback: undefined,
                            fallbackRouteParams: // If we're in production or we're debugging the fallback
                            // shell then we should postpone when dynamic params are
                            // accessed.
                            isProduction || isDebugFallbackShell ? getFallbackRouteParams(pathname) : null
                        }), {
                        routeKind: RouteKind.APP_PAGE,
                        incrementalCache,
                        isRoutePPREnabled,
                        isFallback: true
                    });
                }
                // If the fallback response was set to null, then we should return null.
                if (fallbackResponse === null) return null;
                // Otherwise, if we did get a fallback response, we should return it.
                if (fallbackResponse) {
                    // Remove the cache control from the response to prevent it from being
                    // used in the surrounding cache.
                    delete fallbackResponse.cacheControl;
                    return fallbackResponse;
                }
            }
            // Only requests that aren't revalidating can be resumed. If we have the
            // minimal postponed data, then we should resume the render with it.
            const postponed = !isOnDemandRevalidate && !isRevalidating && minimalPostponed ? minimalPostponed : undefined;
            // When we're in minimal mode, if we're trying to debug the static shell,
            // we should just return nothing instead of resuming the dynamic render.
            if ((isDebugStaticShell || isDebugDynamicAccesses) && typeof postponed !== 'undefined') {
                return {
                    cacheControl: {
                        revalidate: 1,
                        expire: undefined
                    },
                    value: {
                        kind: CachedRouteKind.PAGES,
                        html: RenderResult.fromStatic(''),
                        pageData: {},
                        headers: undefined,
                        status: undefined
                    }
                };
            }
            // If this is a dynamic route with PPR enabled and the default route
            // matches were set, then we should pass the fallback route params to
            // the renderer as this is a fallback revalidation request.
            const fallbackRouteParams = isDynamic && isRoutePPREnabled && (getRequestMeta(req, 'renderFallbackShell') || isDebugFallbackShell) ? getFallbackRouteParams(pathname) : null;
            // Perform the render.
            return doRender({
                postponed,
                pagesFallback: undefined,
                fallbackRouteParams
            });
        };
        if (process.env.NEXT_RUNTIME !== 'edge' && // default _error module in dev doesn't have handler yet
        components.ComponentMod.handler && (isPagesRouteModule(components.routeModule) || isAppRouteRouteModule(components.routeModule) || isAppPageRouteModule(components.routeModule))) {
            if ((routeModule == null ? void 0 : routeModule.isDev) && isDynamicRoute(pathname) && (components.getStaticPaths || isAppPath)) {
                await this.getStaticPaths({
                    pathname,
                    requestHeaders: req.headers,
                    page: components.page,
                    isAppPath
                });
            }
            await doRender({
                postponed: undefined,
                pagesFallback: false,
                fallbackRouteParams: null
            });
            return null;
        }
        const cacheEntry = await this.responseCache.get(ssgCacheKey, responseGenerator, {
            routeKind: // If the route module is not defined, we can assume it's a page being
            // rendered and thus check isAppPath.
            (routeModule == null ? void 0 : routeModule.definition.kind) ?? (isAppPath ? RouteKind.APP_PAGE : RouteKind.PAGES),
            incrementalCache,
            isOnDemandRevalidate,
            isPrefetch: req.headers.purpose === 'prefetch',
            isRoutePPREnabled
        });
        if (isPreviewMode) {
            res.setHeader('Cache-Control', 'private, no-cache, no-store, max-age=0, must-revalidate');
        }
        if (!cacheEntry) {
            if (ssgCacheKey && !(isOnDemandRevalidate && revalidateOnlyGenerated) && !isPagesRouteModule(components.routeModule) && !isAppRouteRouteModule(components.routeModule) && !isAppPageRouteModule(components.routeModule)) {
                // A cache entry might not be generated if a response is written
                // in `getInitialProps` or `getServerSideProps`, but those shouldn't
                // have a cache key. If we do have a cache key but we don't end up
                // with a cache entry, then either Next.js or the application has a
                // bug that needs fixing.
                throw Object.defineProperty(new Error('invariant: cache entry required but not generated'), "__NEXT_ERROR_CODE", {
                    value: "E62",
                    enumerable: false,
                    configurable: true
                });
            }
            return null;
        }
        const didPostpone = ((_cacheEntry_value = cacheEntry.value) == null ? void 0 : _cacheEntry_value.kind) === CachedRouteKind.APP_PAGE && typeof cacheEntry.value.postponed === 'string';
        if (isSSG && // We don't want to send a cache header for requests that contain dynamic
        // data. If this is a Dynamic RSC request or wasn't a Prefetch RSC
        // request, then we should set the cache header.
        !isDynamicRSCRequest && (!didPostpone || isPrefetchRSCRequest)) {
            if (!this.minimalMode) {
                // set x-nextjs-cache header to match the header
                // we set for the image-optimizer
                res.setHeader('x-nextjs-cache', isOnDemandRevalidate ? 'REVALIDATED' : cacheEntry.isMiss ? 'MISS' : cacheEntry.isStale ? 'STALE' : 'HIT');
            }
            // Set a header used by the client router to signal the response is static
            // and should respect the `static` cache staleTime value.
            res.setHeader(NEXT_IS_PRERENDER_HEADER, '1');
        }
        const { value: cachedData } = cacheEntry;
        // If the cache value is an image, we should error early.
        if ((cachedData == null ? void 0 : cachedData.kind) === CachedRouteKind.IMAGE) {
            throw Object.defineProperty(new InvariantError('SSG should not return an image cache value'), "__NEXT_ERROR_CODE", {
                value: "E659",
                enumerable: false,
                configurable: true
            });
        }
        // Coerce the cache control parameter from the render.
        let cacheControl;
        // If this is a resume request in minimal mode it is streamed with dynamic
        // content and should not be cached.
        if (minimalPostponed) {
            cacheControl = {
                revalidate: 0,
                expire: undefined
            };
        } else if (this.minimalMode && isRSCRequest && !isPrefetchRSCRequest && isRoutePPREnabled) {
            cacheControl = {
                revalidate: 0,
                expire: undefined
            };
        } else if (!this.renderOpts.dev || hasServerProps && !isNextDataRequest) {
            // If this is a preview mode request, we shouldn't cache it
            if (isPreviewMode) {
                cacheControl = {
                    revalidate: 0,
                    expire: undefined
                };
            } else if (!isSSG) {
                if (!res.getHeader('Cache-Control')) {
                    cacheControl = {
                        revalidate: 0,
                        expire: undefined
                    };
                }
            } else if (is404Page) {
                const notFoundRevalidate = getRequestMeta(req, 'notFoundRevalidate');
                cacheControl = {
                    revalidate: typeof notFoundRevalidate === 'undefined' ? 0 : notFoundRevalidate,
                    expire: undefined
                };
            } else if (is500Page) {
                cacheControl = {
                    revalidate: 0,
                    expire: undefined
                };
            } else if (cacheEntry.cacheControl) {
                // If the cache entry has a cache control with a revalidate value that's
                // a number, use it.
                if (typeof cacheEntry.cacheControl.revalidate === 'number') {
                    var _cacheEntry_cacheControl;
                    if (cacheEntry.cacheControl.revalidate < 1) {
                        throw Object.defineProperty(new Error(`Invalid revalidate configuration provided: ${cacheEntry.cacheControl.revalidate} < 1`), "__NEXT_ERROR_CODE", {
                            value: "E22",
                            enumerable: false,
                            configurable: true
                        });
                    }
                    cacheControl = {
                        revalidate: cacheEntry.cacheControl.revalidate,
                        expire: ((_cacheEntry_cacheControl = cacheEntry.cacheControl) == null ? void 0 : _cacheEntry_cacheControl.expire) ?? this.nextConfig.expireTime
                    };
                } else {
                    cacheControl = {
                        revalidate: CACHE_ONE_YEAR,
                        expire: undefined
                    };
                }
            }
        }
        cacheEntry.cacheControl = cacheControl;
        if (typeof segmentPrefetchHeader === 'string' && (cachedData == null ? void 0 : cachedData.kind) === CachedRouteKind.APP_PAGE && cachedData.segmentData) {
            var _cachedData_headers;
            // This is a prefetch request issued by the client Segment Cache. These
            // should never reach the application layer (lambda). We should either
            // respond from the cache (HIT) or respond with 204 No Content (MISS).
            // Set a header to indicate that PPR is enabled for this route. This
            // lets the client distinguish between a regular cache miss and a cache
            // miss due to PPR being disabled. In other contexts this header is used
            // to indicate that the response contains dynamic data, but here we're
            // only using it to indicate that the feature is enabled — the segment
            // response itself contains whether the data is dynamic.
            res.setHeader(NEXT_DID_POSTPONE_HEADER, '2');
            // Add the cache tags header to the response if it exists and we're in
            // minimal mode while rendering a static page.
            const tags = (_cachedData_headers = cachedData.headers) == null ? void 0 : _cachedData_headers[NEXT_CACHE_TAGS_HEADER];
            if (this.minimalMode && isSSG && tags && typeof tags === 'string') {
                res.setHeader(NEXT_CACHE_TAGS_HEADER, tags);
            }
            const matchedSegment = cachedData.segmentData.get(segmentPrefetchHeader);
            if (matchedSegment !== undefined) {
                // Cache hit
                return {
                    type: 'rsc',
                    body: RenderResult.fromStatic(matchedSegment),
                    // TODO: Eventually this should use cache control of the individual
                    // segment, not the whole page.
                    cacheControl: cacheEntry.cacheControl
                };
            }
            // Cache miss. Either a cache entry for this route has not been generated
            // (which technically should not be possible when PPR is enabled, because
            // at a minimum there should always be a fallback entry) or there's no
            // match for the requested segment. Respond with a 204 No Content. We
            // don't bother to respond with 404, because these requests are only
            // issued as part of a prefetch.
            res.statusCode = 204;
            return {
                type: 'rsc',
                body: RenderResult.fromStatic(''),
                cacheControl: cacheEntry == null ? void 0 : cacheEntry.cacheControl
            };
        }
        // If there's a callback for `onCacheEntry`, call it with the cache entry
        // and the revalidate options.
        const onCacheEntry = getRequestMeta(req, 'onCacheEntry');
        if (onCacheEntry) {
            var _cacheEntry_value1, _cacheEntry_value2;
            const finished = await onCacheEntry({
                ...cacheEntry,
                // TODO: remove this when upstream doesn't
                // always expect this value to be "PAGE"
                value: {
                    ...cacheEntry.value,
                    kind: ((_cacheEntry_value1 = cacheEntry.value) == null ? void 0 : _cacheEntry_value1.kind) === CachedRouteKind.APP_PAGE ? 'PAGE' : (_cacheEntry_value2 = cacheEntry.value) == null ? void 0 : _cacheEntry_value2.kind
                }
            }, {
                url: getRequestMeta(req, 'initURL')
            });
            if (finished) {
                // TODO: maybe we have to end the request?
                return null;
            }
        }
        if (!cachedData) {
            var _cacheEntry_cacheControl1;
            // add revalidate metadata before rendering 404 page
            // so that we can use this as source of truth for the
            // cache-control header instead of what the 404 page returns
            // for the revalidate value
            addRequestMeta(req, 'notFoundRevalidate', (_cacheEntry_cacheControl1 = cacheEntry.cacheControl) == null ? void 0 : _cacheEntry_cacheControl1.revalidate);
            // If cache control is already set on the response we don't
            // override it to allow users to customize it via next.config
            if (cacheEntry.cacheControl && !res.getHeader('Cache-Control')) {
                res.setHeader('Cache-Control', getCacheControlHeader(cacheEntry.cacheControl));
            }
            if (isNextDataRequest) {
                res.statusCode = 404;
                res.body('{"notFound":true}').send();
                return null;
            }
            if (this.renderOpts.dev) {
                addRequestMeta(req, 'developmentNotFoundSourcePage', pathname);
            }
            await this.render404(req, res, {
                pathname,
                query
            }, false);
            return null;
        } else if (cachedData.kind === CachedRouteKind.REDIRECT) {
            // If cache control is already set on the response we don't
            // override it to allow users to customize it via next.config
            if (cacheEntry.cacheControl && !res.getHeader('Cache-Control')) {
                res.setHeader('Cache-Control', getCacheControlHeader(cacheEntry.cacheControl));
            }
            if (isNextDataRequest) {
                return {
                    type: 'json',
                    body: RenderResult.fromStatic(// @TODO: Handle flight data.
                    JSON.stringify(cachedData.props)),
                    cacheControl: cacheEntry.cacheControl
                };
            } else {
                await handleRedirect(cachedData.props);
                return null;
            }
        } else if (cachedData.kind === CachedRouteKind.APP_ROUTE) {
            // this is handled inside the app_route handler fully
            throw Object.defineProperty(new Error(`Invariant: unexpected APP_ROUTE cache data`), "__NEXT_ERROR_CODE", {
                value: "E702",
                enumerable: false,
                configurable: true
            });
        } else if (cachedData.kind === CachedRouteKind.APP_PAGE) {
            var _cachedData_headers1;
            // If the request has a postponed state and it's a resume request we
            // should error.
            if (didPostpone && minimalPostponed) {
                throw Object.defineProperty(new Error('Invariant: postponed state should not be present on a resume request'), "__NEXT_ERROR_CODE", {
                    value: "E396",
                    enumerable: false,
                    configurable: true
                });
            }
            if (cachedData.headers) {
                const headers = {
                    ...cachedData.headers
                };
                if (!this.minimalMode || !isSSG) {
                    delete headers[NEXT_CACHE_TAGS_HEADER];
                }
                for (let [key, value] of Object.entries(headers)){
                    if (typeof value === 'undefined') continue;
                    if (Array.isArray(value)) {
                        for (const v of value){
                            res.appendHeader(key, v);
                        }
                    } else if (typeof value === 'number') {
                        value = value.toString();
                        res.appendHeader(key, value);
                    } else {
                        res.appendHeader(key, value);
                    }
                }
            }
            // Add the cache tags header to the response if it exists and we're in
            // minimal mode while rendering a static page.
            const tags = (_cachedData_headers1 = cachedData.headers) == null ? void 0 : _cachedData_headers1[NEXT_CACHE_TAGS_HEADER];
            if (this.minimalMode && isSSG && tags && typeof tags === 'string') {
                res.setHeader(NEXT_CACHE_TAGS_HEADER, tags);
            }
            // If the request is a data request, then we shouldn't set the status code
            // from the response because it should always be 200. This should be gated
            // behind the experimental PPR flag.
            if (cachedData.status && (!isRSCRequest || !isRoutePPREnabled)) {
                res.statusCode = cachedData.status;
            }
            // Redirect information is encoded in RSC payload, so we don't need to use redirect status codes
            if (!this.minimalMode && cachedData.status && RedirectStatusCode[cachedData.status] && isRSCRequest) {
                res.statusCode = 200;
            }
            // Mark that the request did postpone.
            if (didPostpone) {
                res.setHeader(NEXT_DID_POSTPONE_HEADER, '1');
            }
            // we don't go through this block when preview mode is true
            // as preview mode is a dynamic request (bypasses cache) and doesn't
            // generate both HTML and payloads in the same request so continue to just
            // return the generated payload
            if (isRSCRequest && !isPreviewMode) {
                // If this is a dynamic RSC request, then stream the response.
                if (typeof cachedData.rscData === 'undefined') {
                    if (cachedData.postponed) {
                        throw Object.defineProperty(new Error('Invariant: Expected postponed to be undefined'), "__NEXT_ERROR_CODE", {
                            value: "E372",
                            enumerable: false,
                            configurable: true
                        });
                    }
                    return {
                        type: 'rsc',
                        body: cachedData.html,
                        // Dynamic RSC responses cannot be cached, even if they're
                        // configured with `force-static` because we have no way of
                        // distinguishing between `force-static` and pages that have no
                        // postponed state.
                        // TODO: distinguish `force-static` from pages with no postponed state (static)
                        cacheControl: isDynamicRSCRequest ? {
                            revalidate: 0,
                            expire: undefined
                        } : cacheEntry.cacheControl
                    };
                }
                // As this isn't a prefetch request, we should serve the static flight
                // data.
                return {
                    type: 'rsc',
                    body: RenderResult.fromStatic(cachedData.rscData),
                    cacheControl: cacheEntry.cacheControl
                };
            }
            // This is a request for HTML data.
            let body = cachedData.html;
            // If there's no postponed state, we should just serve the HTML. This
            // should also be the case for a resume request because it's completed
            // as a server render (rather than a static render).
            if (!didPostpone || this.minimalMode) {
                return {
                    type: 'html',
                    body,
                    cacheControl: cacheEntry.cacheControl
                };
            }
            // If we're debugging the static shell or the dynamic API accesses, we
            // should just serve the HTML without resuming the render. The returned
            // HTML will be the static shell so all the Dynamic API's will be used
            // during static generation.
            if (isDebugStaticShell || isDebugDynamicAccesses) {
                // Since we're not resuming the render, we need to at least add the
                // closing body and html tags to create valid HTML.
                body.chain(new ReadableStream({
                    start (controller) {
                        controller.enqueue(ENCODED_TAGS.CLOSED.BODY_AND_HTML);
                        controller.close();
                    }
                }));
                return {
                    type: 'html',
                    body,
                    cacheControl: {
                        revalidate: 0,
                        expire: undefined
                    }
                };
            }
            // This request has postponed, so let's create a new transformer that the
            // dynamic data can pipe to that will attach the dynamic data to the end
            // of the response.
            const transformer = new TransformStream();
            body.chain(transformer.readable);
            // Perform the render again, but this time, provide the postponed state.
            // We don't await because we want the result to start streaming now, and
            // we've already chained the transformer's readable to the render result.
            doRender({
                postponed: cachedData.postponed,
                pagesFallback: undefined,
                // This is a resume render, not a fallback render, so we don't need to
                // set this.
                fallbackRouteParams: null
            }).then(async (result)=>{
                var _result_value;
                if (!result) {
                    throw Object.defineProperty(new Error('Invariant: expected a result to be returned'), "__NEXT_ERROR_CODE", {
                        value: "E463",
                        enumerable: false,
                        configurable: true
                    });
                }
                if (((_result_value = result.value) == null ? void 0 : _result_value.kind) !== CachedRouteKind.APP_PAGE) {
                    var _result_value1;
                    throw Object.defineProperty(new Error(`Invariant: expected a page response, got ${(_result_value1 = result.value) == null ? void 0 : _result_value1.kind}`), "__NEXT_ERROR_CODE", {
                        value: "E305",
                        enumerable: false,
                        configurable: true
                    });
                }
                // Pipe the resume result to the transformer.
                await result.value.html.pipeTo(transformer.writable);
            }).catch((err)=>{
                // An error occurred during piping or preparing the render, abort
                // the transformers writer so we can terminate the stream.
                transformer.writable.abort(err).catch((e)=>{
                    console.error("couldn't abort transformer", e);
                });
            });
            return {
                type: 'html',
                body,
                // We don't want to cache the response if it has postponed data because
                // the response being sent to the client it's dynamic parts are streamed
                // to the client on the same request.
                cacheControl: {
                    revalidate: 0,
                    expire: undefined
                }
            };
        } else if (isNextDataRequest) {
            return {
                type: 'json',
                body: RenderResult.fromStatic(JSON.stringify(cachedData.pageData)),
                cacheControl: cacheEntry.cacheControl
            };
        } else {
            return {
                type: 'html',
                body: cachedData.html,
                cacheControl: cacheEntry.cacheControl
            };
        }
    }
    stripNextDataPath(path, stripLocale = true) {
        if (path.includes(this.buildId)) {
            const splitPath = path.substring(path.indexOf(this.buildId) + this.buildId.length);
            path = denormalizePagePath(splitPath.replace(/\.json$/, ''));
        }
        if (this.localeNormalizer && stripLocale) {
            return this.localeNormalizer.normalize(path);
        }
        return path;
    }
    // map the route to the actual bundle name
    getOriginalAppPaths(route) {
        if (this.enabledDirectories.app) {
            var _this_appPathRoutes;
            const originalAppPath = (_this_appPathRoutes = this.appPathRoutes) == null ? void 0 : _this_appPathRoutes[route];
            if (!originalAppPath) {
                return null;
            }
            return originalAppPath;
        }
        return null;
    }
    async renderPageComponent(ctx, bubbleNoFallback) {
        var _this_nextConfig_experimental_sri;
        const { query, pathname } = ctx;
        const appPaths = this.getOriginalAppPaths(pathname);
        const isAppPath = Array.isArray(appPaths);
        let page = pathname;
        if (isAppPath) {
            // the last item in the array is the root page, if there are parallel routes
            page = appPaths[appPaths.length - 1];
        }
        const result = await this.findPageComponents({
            locale: getRequestMeta(ctx.req, 'locale'),
            page,
            query,
            params: ctx.renderOpts.params || {},
            isAppPath,
            sriEnabled: !!((_this_nextConfig_experimental_sri = this.nextConfig.experimental.sri) == null ? void 0 : _this_nextConfig_experimental_sri.algorithm),
            appPaths,
            // Ensuring for loading page component routes is done via the matcher.
            shouldEnsure: false
        });
        if (result) {
            getTracer().setRootSpanAttribute('next.route', pathname);
            try {
                return await this.renderToResponseWithComponents(ctx, result);
            } catch (err) {
                const isNoFallbackError = err instanceof NoFallbackError;
                if (!isNoFallbackError || isNoFallbackError && bubbleNoFallback) {
                    throw err;
                }
            }
        }
        return false;
    }
    async renderToResponse(ctx) {
        return getTracer().trace(BaseServerSpan.renderToResponse, {
            spanName: `rendering page`,
            attributes: {
                'next.route': ctx.pathname
            }
        }, async ()=>{
            return this.renderToResponseImpl(ctx);
        });
    }
    async renderToResponseImpl(ctx) {
        var _this_i18nProvider;
        const { req, res, query, pathname } = ctx;
        let page = pathname;
        const bubbleNoFallback = getRequestMeta(ctx.req, 'bubbleNoFallback') ?? false;
        if (!this.minimalMode && this.nextConfig.experimental.validateRSCRequestHeaders) {
            addRequestMeta(ctx.req, 'cacheBustingSearchParam', query[NEXT_RSC_UNION_QUERY]);
        }
        delete query[NEXT_RSC_UNION_QUERY];
        const options = {
            i18n: (_this_i18nProvider = this.i18nProvider) == null ? void 0 : _this_i18nProvider.fromRequest(req, pathname)
        };
        try {
            for await (const match of this.matchers.matchAll(pathname, options)){
                // when a specific invoke-output is meant to be matched
                // ensure a prior dynamic route/page doesn't take priority
                const invokeOutput = getRequestMeta(ctx.req, 'invokeOutput');
                if (!this.minimalMode && typeof invokeOutput === 'string' && isDynamicRoute(invokeOutput || '') && invokeOutput !== match.definition.pathname) {
                    continue;
                }
                const result = await this.renderPageComponent({
                    ...ctx,
                    pathname: match.definition.pathname,
                    renderOpts: {
                        ...ctx.renderOpts,
                        params: match.params
                    }
                }, bubbleNoFallback);
                if (result !== false) return result;
            }
            // currently edge functions aren't receiving the x-matched-path
            // header so we need to fallback to matching the current page
            // when we weren't able to match via dynamic route to handle
            // the rewrite case
            // @ts-expect-error extended in child class web-server
            if (this.serverOptions.webServerConfig) {
                // @ts-expect-error extended in child class web-server
                ctx.pathname = this.serverOptions.webServerConfig.page;
                const result = await this.renderPageComponent(ctx, bubbleNoFallback);
                if (result !== false) return result;
            }
        } catch (error) {
            const err = getProperError(error);
            if (error instanceof MissingStaticPage) {
                console.error('Invariant: failed to load static page', JSON.stringify({
                    page,
                    url: ctx.req.url,
                    matchedPath: ctx.req.headers[MATCHED_PATH_HEADER],
                    initUrl: getRequestMeta(ctx.req, 'initURL'),
                    didRewrite: !!getRequestMeta(ctx.req, 'rewroteURL'),
                    rewroteUrl: getRequestMeta(ctx.req, 'rewroteURL')
                }, null, 2));
                throw err;
            }
            if (err instanceof NoFallbackError && bubbleNoFallback) {
                throw err;
            }
            if (err instanceof DecodeError || err instanceof NormalizeError) {
                res.statusCode = 400;
                return await this.renderErrorToResponse(ctx, err);
            }
            res.statusCode = 500;
            // if pages/500 is present we still need to trigger
            // /_error `getInitialProps` to allow reporting error
            if (await this.hasPage('/500')) {
                addRequestMeta(ctx.req, 'customErrorRender', true);
                await this.renderErrorToResponse(ctx, err);
                removeRequestMeta(ctx.req, 'customErrorRender');
            }
            const isWrappedError = err instanceof WrappedBuildError;
            if (!isWrappedError) {
                if (this.minimalMode && process.env.NEXT_RUNTIME !== 'edge' || this.renderOpts.dev) {
                    if (isError(err)) err.page = page;
                    throw err;
                }
                this.logError(getProperError(err));
            }
            const response = await this.renderErrorToResponse(ctx, isWrappedError ? err.innerError : err);
            return response;
        }
        const middleware = await this.getMiddleware();
        if (middleware && !!ctx.req.headers['x-nextjs-data'] && (!res.statusCode || res.statusCode === 200 || res.statusCode === 404)) {
            const locale = getRequestMeta(req, 'locale');
            res.setHeader('x-nextjs-matched-path', `${locale ? `/${locale}` : ''}${pathname}`);
            res.statusCode = 200;
            res.setHeader('content-type', 'application/json');
            res.body('{}');
            res.send();
            return null;
        }
        res.statusCode = 404;
        return this.renderErrorToResponse(ctx, null);
    }
    async renderToHTML(req, res, pathname, query = {}) {
        return getTracer().trace(BaseServerSpan.renderToHTML, async ()=>{
            return this.renderToHTMLImpl(req, res, pathname, query);
        });
    }
    async renderToHTMLImpl(req, res, pathname, query = {}) {
        return this.getStaticHTML((ctx)=>this.renderToResponse(ctx), {
            req,
            res,
            pathname,
            query
        });
    }
    async renderError(err, req, res, pathname, query = {}, setHeaders = true) {
        return getTracer().trace(BaseServerSpan.renderError, async ()=>{
            return this.renderErrorImpl(err, req, res, pathname, query, setHeaders);
        });
    }
    async renderErrorImpl(err, req, res, pathname, query = {}, setHeaders = true) {
        if (setHeaders) {
            res.setHeader('Cache-Control', 'private, no-cache, no-store, max-age=0, must-revalidate');
        }
        return this.pipe(async (ctx)=>{
            const response = await this.renderErrorToResponse(ctx, err);
            if (this.minimalMode && res.statusCode === 500) {
                throw err;
            }
            return response;
        }, {
            req,
            res,
            pathname,
            query
        });
    }
    async renderErrorToResponse(ctx, err) {
        return getTracer().trace(BaseServerSpan.renderErrorToResponse, async ()=>{
            return this.renderErrorToResponseImpl(ctx, err);
        });
    }
    async renderErrorToResponseImpl(ctx, err) {
        // Short-circuit favicon.ico in development to avoid compiling 404 page when the app has no favicon.ico.
        // Since favicon.ico is automatically requested by the browser.
        if (this.renderOpts.dev && ctx.pathname === '/favicon.ico') {
            return {
                type: 'html',
                body: RenderResult.fromStatic('')
            };
        }
        const { res, query } = ctx;
        try {
            let result = null;
            const is404 = res.statusCode === 404;
            let using404Page = false;
            if (is404) {
                if (this.enabledDirectories.app) {
                    // Use the not-found entry in app directory
                    result = await this.findPageComponents({
                        locale: getRequestMeta(ctx.req, 'locale'),
                        page: UNDERSCORE_NOT_FOUND_ROUTE_ENTRY,
                        query,
                        params: {},
                        isAppPath: true,
                        shouldEnsure: true,
                        url: ctx.req.url
                    });
                    using404Page = result !== null;
                }
                if (!result && await this.hasPage('/404')) {
                    result = await this.findPageComponents({
                        locale: getRequestMeta(ctx.req, 'locale'),
                        page: '/404',
                        query,
                        params: {},
                        isAppPath: false,
                        // Ensuring can't be done here because you never "match" a 404 route.
                        shouldEnsure: true,
                        url: ctx.req.url
                    });
                    using404Page = result !== null;
                }
            }
            let statusPage = `/${res.statusCode}`;
            if (!getRequestMeta(ctx.req, 'customErrorRender') && !result && STATIC_STATUS_PAGES.includes(statusPage)) {
                // skip ensuring /500 in dev mode as it isn't used and the
                // dev overlay is used instead
                if (statusPage !== '/500' || !this.renderOpts.dev) {
                    result = await this.findPageComponents({
                        locale: getRequestMeta(ctx.req, 'locale'),
                        page: statusPage,
                        query,
                        params: {},
                        isAppPath: false,
                        // Ensuring can't be done here because you never "match" a 500
                        // route.
                        shouldEnsure: true,
                        url: ctx.req.url
                    });
                }
            }
            if (!result) {
                result = await this.findPageComponents({
                    locale: getRequestMeta(ctx.req, 'locale'),
                    page: '/_error',
                    query,
                    params: {},
                    isAppPath: false,
                    // Ensuring can't be done here because you never "match" an error
                    // route.
                    shouldEnsure: true,
                    url: ctx.req.url
                });
                statusPage = '/_error';
            }
            if (process.env.NODE_ENV !== 'production' && !using404Page && await this.hasPage('/_error') && !await this.hasPage('/404')) {
                this.customErrorNo404Warn();
            }
            if (!result) {
                // this can occur when a project directory has been moved/deleted
                // which is handled in the parent process in development
                if (this.renderOpts.dev) {
                    return {
                        type: 'html',
                        // wait for dev-server to restart before refreshing
                        body: RenderResult.fromStatic(`
              <pre>missing required error components, refreshing...</pre>
              <script>
                async function check() {
                  const res = await fetch(location.href).catch(() => ({}))

                  if (res.status === 200) {
                    location.reload()
                  } else {
                    setTimeout(check, 1000)
                  }
                }
                check()
              </script>`)
                    };
                }
                throw new WrappedBuildError(Object.defineProperty(new Error('missing required error components'), "__NEXT_ERROR_CODE", {
                    value: "E60",
                    enumerable: false,
                    configurable: true
                }));
            }
            // If the page has a route module, use it for the new match. If it doesn't
            // have a route module, remove the match.
            if (result.components.routeModule) {
                addRequestMeta(ctx.req, 'match', {
                    definition: result.components.routeModule.definition,
                    params: undefined
                });
            } else {
                removeRequestMeta(ctx.req, 'match');
            }
            try {
                return await this.renderToResponseWithComponents({
                    ...ctx,
                    pathname: statusPage,
                    renderOpts: {
                        ...ctx.renderOpts,
                        err
                    }
                }, result);
            } catch (maybeFallbackError) {
                if (maybeFallbackError instanceof NoFallbackError) {
                    throw Object.defineProperty(new Error('invariant: failed to render error page'), "__NEXT_ERROR_CODE", {
                        value: "E55",
                        enumerable: false,
                        configurable: true
                    });
                }
                throw maybeFallbackError;
            }
        } catch (error) {
            const renderToHtmlError = getProperError(error);
            const isWrappedError = renderToHtmlError instanceof WrappedBuildError;
            if (!isWrappedError) {
                this.logError(renderToHtmlError);
            }
            res.statusCode = 500;
            const fallbackComponents = await this.getFallbackErrorComponents(ctx.req.url);
            if (fallbackComponents) {
                // There was an error, so use it's definition from the route module
                // to add the match to the request.
                addRequestMeta(ctx.req, 'match', {
                    definition: fallbackComponents.routeModule.definition,
                    params: undefined
                });
                return this.renderToResponseWithComponents({
                    ...ctx,
                    pathname: '/_error',
                    renderOpts: {
                        ...ctx.renderOpts,
                        // We render `renderToHtmlError` here because `err` is
                        // already captured in the stacktrace.
                        err: isWrappedError ? renderToHtmlError.innerError : renderToHtmlError
                    }
                }, {
                    query,
                    components: fallbackComponents
                });
            }
            return {
                type: 'html',
                body: RenderResult.fromStatic('Internal Server Error')
            };
        }
    }
    async renderErrorToHTML(err, req, res, pathname, query = {}) {
        return this.getStaticHTML((ctx)=>this.renderErrorToResponse(ctx, err), {
            req,
            res,
            pathname,
            query
        });
    }
    async render404(req, res, parsedUrl, setHeaders = true) {
        const { pathname, query } = parsedUrl ? parsedUrl : parseUrl(req.url, true);
        // Ensure the locales are provided on the request meta.
        if (this.nextConfig.i18n) {
            if (!getRequestMeta(req, 'locale')) {
                addRequestMeta(req, 'locale', this.nextConfig.i18n.defaultLocale);
            }
            addRequestMeta(req, 'defaultLocale', this.nextConfig.i18n.defaultLocale);
        }
        res.statusCode = 404;
        return this.renderError(null, req, res, pathname, query, setHeaders);
    }
}

//# sourceMappingURL=base-server.js.map